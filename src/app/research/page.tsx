"use client";
import Image from "next/image";
import React, { useEffect, useState } from "react";
import externalLinkIcon from "../../../public/Button.png";
import brainLogo from "../../../public/brain_logo.png";
import { researchData } from "../../utils/researchData";

export default function Page() {
  const [isMobile, setIsMobile] = useState(false);
  const [showAll, setShowAll] = useState(false);

  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth < 768) {
        setIsMobile(true);
      } else {
        setIsMobile(false);
      }
    };
    window.addEventListener("resize", handleResize);
    handleResize();
    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, []);

  const handleToggle = () => {
    setShowAll(!showAll);
  };

  return (
    <div className="lg:w-[70%] mx-auto min-h-screen ">
      <div className="flex justify-center gap-14 pt-16 pb-12 md:mx-6">
        <Image
          className="lg:w-[150px] lg:h-[120px] md:w-[130px] md:h-[80px] sm:mt-10 lg:mt-7 hide-on-mobile"
          src={brainLogo}
          alt="subconscious.ai logo"
        />
        <div className="m-6 text-center md:text-start">
          <h1 className="font-roboto font-semibold lg:text-6xl text-3xl pb-[10px] pr-3 leading-tight text-white">
            Research
          </h1>
          <p className="pt-[10px] font-roboto font-normal lg:text-xl md:text-lg text-white leading-6 ">
            Explore our curated collection of secondary academic research,
            focused on how LLMs can simulate human behavior. This research
            supports our AI-driven approach to market research.
          </p>
        </div>
      </div>
      <div className="relative z-10 mb-20 sm:mb-0 mx-auto w-full">
        <div className="grid lg:grid-cols-3 md:grid-cols-2 gap-6 m-6 ">
          {isMobile
            ? researchData
                .slice(0, showAll ? researchData.length : 10)
                .map(
                  (
                    { text, href }: { text: string; href: string },
                    index: number
                  ) => (
                    <div
                      key={index}
                      className="relative border border-[#FFFFFF14]  p-4 py-5 rounded-[20px] bg-[#FFFFFF14]  flex justify-between items-center h-[135px]"
                    >
                      <p className="font-medium text-base font-roboto text-white w-[90%] overflow-hidden line-clamp">
                        {text}
                      </p>
                      <a
                        aria-label="external link icon"
                        href={href}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="pl-3 w-11"
                      >
                        <Image
                          src={externalLinkIcon}
                          alt={text}
                          width={44}
                          height={44}
                        />
                      </a>
                    </div>
                  )
                )
            : researchData.map(
                (
                  { text, href }: { text: string; href: string },
                  index: number
                ) => (
                  <div
                    key={index}
                    className="relative border border-[#FFFFFF14]  p-4 py-5 rounded-[20px] bg-[#FFFFFF14]  flex justify-between items-center h-[135px]"
                  >
                    <p className="font-medium text-base font-roboto text-white w-[90%] overflow-hidden line-clamp">
                      {text}
                    </p>
                    <a
                      aria-label="external link icon"
                      href={href}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="pl-3 w-11"
                    >
                      <Image
                        src={externalLinkIcon}
                        alt={text}
                        width={44}
                        height={44}
                      />
                    </a>
                  </div>
                )
              )}
        </div>
        <style jsx>{`
          .line-clamp {
            display: -webkit-box;
            -webkit-line-clamp: 4;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        `}</style>
        <div
          className={`absolute block md:hidden z-70 h-[120px]  w-[89%] rounded-[20px] mx-6 mb-8 bottom-0 ${showAll ? "transform translate-y-[100%] " : "transform translate-y-[50%] bg-gradient-to-t from-[#000105] to-transparent"} `}
        >
          {isMobile && researchData.length > 10 && (
            <div className="flex justify-center items-end">
              <button
                className="mt-16 py-3 px-6 rounded-[20px] text-white font-roboto font-semibold text-base"
                onClick={handleToggle}
              >
                {showAll ? "See Less" : "See All"}
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
