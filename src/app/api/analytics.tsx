"use client";
import { useEffect, useRef } from "react";
import { usePathname } from "next/navigation";
import { AnalyticsBrowser } from "@segment/analytics-next";

export default function Analytics() {
  const pathname = usePathname();
  const analyticsRef = useRef<any>(null);
  const isInitialized = useRef(false);

  useEffect(() => {
    // Delay analytics loading to not block initial render
    const timer = setTimeout(() => {
      if (!isInitialized.current && process.env.NEXT_PUBLIC_SEGMENT_WRITE_KEY) {
        analyticsRef.current = AnalyticsBrowser.load({
          writeKey: process.env.NEXT_PUBLIC_SEGMENT_WRITE_KEY!,
        });
        isInitialized.current = true;
      }
    }, 2000); // Load after 2 seconds

    return () => clearTimeout(timer);
  }, []);

  useEffect(() => {
    // Track page views only after analytics is loaded
    if (analyticsRef.current && isInitialized.current) {
      analyticsRef.current.page({ path: pathname });
    }
  }, [pathname]);

  return null;
}
