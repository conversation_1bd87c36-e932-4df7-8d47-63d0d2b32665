"use client";
import { useEffect } from "react";
import { usePathname } from "next/navigation";
import { AnalyticsBrowser } from "@segment/analytics-next";

export default function Analytics() {
  const pathname = usePathname();

  const analytics = AnalyticsBrowser.load({
    writeKey: process.env.NEXT_PUBLIC_SEGMENT_WRITE_KEY!,
  });

  useEffect(() => {
    analytics.page({ path: pathname });
  }, [pathname]);

  return null;
}
