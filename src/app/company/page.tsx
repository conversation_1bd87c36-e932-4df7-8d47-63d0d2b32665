const RESEARCH = [
  "<PERSON>. <PERSON>",
  "<PERSON><PERSON> <PERSON>",
  "<PERSON><PERSON> <PERSON>",
];

const ENGINEERING = [
  "<PERSON><PERSON>",
  "<PERSON><PERSON><PERSON>",
  "<PERSON><PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON><PERSON>",
  "<PERSON><PERSON>",
  "<PERSON><PERSON><PERSON>",
  "<PERSON><PERSON><PERSON>",
  "<PERSON>",
  "<PERSON><PERSON><PERSON>",
  "<PERSON>",
  "<PERSON><PERSON><PERSON>",
  "<PERSON>",
  "<PERSON><PERSON><PERSON>",
  "<PERSON>",
  "<PERSON><PERSON>",
  "<PERSON><PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON><PERSON>",
  "<PERSON>",
  "Arshuman",
  "<PERSON><PERSON><PERSON>",
];

const OPERATIONS = [
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
];

interface TeamSectionProps {
  title: string;
  names: string[];
}

function TeamSection({ title, names }: TeamSectionProps) {
  return (
    <div className="flex flex-col gap-4 font-medium">
      <h2 className="text-xl lg:text-2xl">{title}</h2>
      <div className="flex flex-row gap-4 flex-wrap">
        {names.map((name) => {
          if (name === "<PERSON>. <PERSON>") {
            return (
              <div
                className="bg-white/10 rounded-lg px-4 py-3 text-base lg:text-xl"
                key={name}
              >
                <a
                  href="https://www.ted.com/talks/malcolm_gladwell_choice_happiness_and_spaghetti_sauce/"
                  target="_blank"
                  rel="noopener noreferrer"
                  style={{ textDecoration: "underline" }}
                >
                  {name}
                </a>
              </div>
            );
          } else {
            return (
              <div
                className="bg-white/10 rounded-lg px-4 py-3 text-base lg:text-xl"
                key={name}
              >
                <p>{name}</p>
              </div>
            );
          }
        })}
      </div>
    </div>
  );
}
export default function Page() {
  return (
    <div className="z-10 mx-8 md:mx-12 py-6 lg:h-auto lg:max-w-6xl lg:py-12 xl:mx-auto flex flex-col gap-10 lg:gap-16 font-roboto text-white">
      {/* ABOUT US */}
      {/* <BackgroundBoxesDemo /> */}
      <div className="">
        <h1 className="text-3xl font-semibold lg:text-company-heading mb-6 lg:mb-10">
          About Us
        </h1>
        <p className="text-2xl lg:text-company-body font-normal">
          Subconscious.ai is an independent research lab exploring{" "}
          <b>new mediums of thought and expanding the imaginative powers</b> of
          the human species.
          {/* Subconscious.ai is revolutionizing market and policy research, backed by a team with proven success in pioneering generative AI products and driving industry-leading innovations. */}
          {/* Subconscious.ai radically accelerates Market Research by <b>augmenting human market survey responses with synthetic respondents</b>. Join us at the curious frontier of two game-changing technologies - <b>LLMs</b> and <b>Causal AI</b>. */}
        </p>
      </div>

      {/* OUR TEAM */}
      <div className="flex flex-col gap-4 font-medium">
        <h1 className="text-3xl font-semibold lg:text-company-heading mb-6 lg:mb-10">
          Our Team
        </h1>
        <h2 className="text-xl lg:text-2xl">Executives</h2>
        <div className="flex flex-col md:flex-row gap-4 justify-center items-center">
          <div className="bg-white/10 rounded-lg px-4 py-3 text-base lg:text-xl h-fit whitespace-nowrap">
            <p>{"Avi Yashchin"}</p>
          </div>
          <div>
            <p className="text-base lg:text-company-details font-normal">
              Previously: Data-driven founder + 2x public exits. Clean Edison
              (acquired by NYSE:$GHC) and Sustainable Properties Real Estate
              (acquired by NASDAQ:$CLSK). Researcher at Two Sigma, JPL, IBM
              Watson Research.
            </p>
          </div>
        </div>
        <div className="flex flex-col md:flex-row gap-4 justify-center items-center">
          <div className="bg-white/10 rounded-lg px-4 py-3 text-base lg:text-xl h-fit whitespace-nowrap">
            <p>{"Dr. Subodh Dubey"}</p>
          </div>
          <div>
            <p className="text-base lg:text-company-details font-normal">
              Behavioural economist with 10+ years of experience across academia
              and industry. Made significant contributions to the field of
              economic choice behaviour.
            </p>
          </div>
        </div>
        <div className="flex flex-col md:flex-row gap-4 justify-center items-center">
          <div className="bg-white/10 rounded-lg px-4 py-3 text-base lg:text-xl h-fit whitespace-nowrap">
            <p>{"Connor Joyce"}</p>
          </div>
          <div>
            <p className="text-base lg:text-company-details font-normal">
              Behavioral change product innovator in enterprise. Previously:
              User Researcher at BetterUp, foundational member of Applied
              Behavioral Science Association. Author &quot;Bridging Intentions
              to Impact.&quot;
            </p>
          </div>
        </div>
        <div className="flex flex-col md:flex-row gap-4 justify-center items-center">
          <div className="bg-white/10 rounded-lg px-4 py-3 text-base lg:text-xl h-fit whitespace-nowrap">
            <p>{"Mahdi Jafari"}</p>
          </div>
          <div>
            <p className="text-base lg:text-company-details font-normal">
              Senior Software Engineer building scalable AI infrastructures.
              Expert in robust backend systems for LLM-powered applications.
            </p>
          </div>
        </div>
        <div className="flex flex-col md:flex-row gap-4 justify-center items-center">
          <div className="bg-white/10 rounded-lg px-4 py-3 text-base lg:text-xl h-fit whitespace-nowrap">
            <p>{"Pablo Cruz"}</p>
          </div>
          <div>
            <p className="text-base lg:text-company-details font-normal">
              Founder with a successful exit. Co-founded JULO Exchange. Scaled
              Booster Fuels to $25M ARR. Launched products at Frog Design.
              Investment Committee Board Member at Cap Table Coalition. VC
              Consultant.
            </p>
          </div>
        </div>
        <div className="flex flex-col md:flex-row gap-4 justify-center items-center">
          <div className="bg-white/10 rounded-lg px-4 py-3 text-base lg:text-xl h-fit whitespace-nowrap">
            <p>{"Aida Enache"}</p>
          </div>
          <div>
            <p className="text-base lg:text-company-details font-normal">
              Product Designer with technical background set to simplify complex
              processed into intuitive, delightful experiences. Helped startups
              launch and enterprises reach new heights.
            </p>
          </div>
        </div>
        <div className="flex flex-col md:flex-row gap-4 justify-center items-center">
          <div className="bg-white/10 rounded-lg px-4 py-3 text-base lg:text-xl h-fit whitespace-nowrap">
            <p>{"Tatevik Karapetyan"}</p>
          </div>
          <div>
            <p className="text-base lg:text-company-details font-normal">
              Machine learning researcher specializing in prompt engineering and
              data visualization. Previously: Data scientist focused on
              statistical modeling and data analysis projects.
            </p>
          </div>
        </div>
      </div>
      <div>
        <TeamSection title="Research" names={RESEARCH} />
      </div>
      <div>
        <TeamSection title="Engineering" names={ENGINEERING} />
      </div>
      <div>
        <TeamSection title="Operations" names={OPERATIONS} />
      </div>

      {/* CAREERS */}
      <div>
        <h1 className="text-3xl font-semibold lg:text-company-heading mb-6 lg:mb-10">
          Careers
        </h1>
        <div className="flex flex-col gap-2 text-2xl lg:text-company-body font-normal leading-normal">
          <p>
            We&apos;re a small, self-funded, fully-distributed team and
            we&apos;re actively hiring!
          </p>
          <p>
            Come help us scale, explore, and build humanist infrastructure
            focused on amplifying the human mind and spirit.
          </p>
          <p>
            If you&apos;re interested in working with us check out our{" "}
            <a
              className="underline"
              href=" https://subconscious-ai.breezy.hr/ "
            >
              jobs page.
            </a>
          </p>
          <p>
            If you&apos;re sure you can help, but don&apos;t see a position that
            fits{" "}
            <a className="underline" href="mailto:<EMAIL>">
              email us.
            </a>
          </p>
          <p>We look forward to hearing from you!</p>
        </div>
      </div>
    </div>
  );
}
