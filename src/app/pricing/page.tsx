import Faqs from "../_components/Faqs";
import SubscriptionCard from "../_components/SubscriptionCard";

export default function Page() {
  return (
    <div className="z-10 mx-auto font-roboto text-white pt-20 lg:px-0 px-4">
      <div className="pb-5">
        <h1 className="text-3xl font-semibold lg:text-company-heading mb-6 lg:mb-10 text-center">
          Research with Subconscious AI
        </h1>
      </div>

      {/* Pricing Cards */}
      <div className="flex flex-col gap-4 font-medium">
        <SubscriptionCard></SubscriptionCard>
        <Faqs></Faqs>
        <div className="line-breaker my-4 "></div>
        <div className="py-10">
          <div className="flex items-center justify-center">
            <h2 className="font-semibold text-center pb-10 md:text-3xl text-2xl">
              Still have questions?
            </h2>
          </div>
          <div className="font-semibold text-center pb-10 md:text-xl text-md">
            Can’t find the answer you’re looking for? Please chat to our
            friendly team.
          </div>
          <div className="flex items-center justify-center">
            <div className="p-0.5 rounded-full flex items-center justify-center bg-gradient-to-r from-cyan-500 to-blue-500 ">
              <a
                className="p-0.5 rounded-full bg-soft-gradient"
                href="mailto:<EMAIL>"
              >
                <span className="block text-white text-roboto px-8 py-3 text-base font-semibold rounded-full bg-[#171C3C] hover:bg-transparent transition duration-300">
                  Contact our team
                </span>
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
