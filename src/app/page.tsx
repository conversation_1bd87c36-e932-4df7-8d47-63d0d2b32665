"use client";
import React, { useEffect, useState, useMemo } from "react";
import dynamic from "next/dynamic";
import Image from "next/image";
import PartnersList from "./_components/PartnersList";
import DynamicInput from "./_components/DynamicInput";
import Title from "./_components/Title";
import Heading from "./_components/Heading";
import Button from "./_components/Button";
const UserType = dynamic(() => import("@/app/_components/UserType"), {
  ssr: false,
});
const LogoList = dynamic(() => import("./_components/LogoList"), {
  ssr: false,
});
const Schedule = dynamic(() => import("./_components/Schedule"), {
  ssr: false,
});
const Brain = dynamic(() => import("./_components/Brain"), { ssr: false });
const CardWithImage = dynamic(() => import("./_components/CardWithImage"), {
  ssr: false,
});
const HeroScrollDemo = dynamic(
  () => import("./_components/TabletDemo").then((mod) => mod.HeroScrollDemo),
  { ssr: false }
);

export default function Home() {
  const [isMobile, setIsMobile] = useState<boolean>(false);

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth <= 768); // Adjust this threshold as needed
    };

    // Initial check
    handleResize();

    // Event listener for window resize
    window.addEventListener("resize", handleResize);

    // Cleanup
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  const partnersSection = useMemo(
    () => (
      <div className="flex flex-col mt-6 gap-8 text-center items-center">
        <Title whiteText="Our Partners" />
        <div className="py-4">
          <PartnersList />
        </div>
      </div>
    ),
    []
  );

  return (
    <div>
      <div className="z-10 mx-10 my-6 lg:h-auto lg:max-w-3xl xl:max-w-5xl lg:my-12 lg:mx-auto flex flex-col gap-8 lg:gap-20">
        <div className="flex flex-col gap-5 lg:gap-2 text-center items-center">
          <div className="flex flex-col w-fit lg:gap-7">
            <Heading
              whiteText="Understand the behavior of your"
              gradientText="users"
              gradientText2="patients"
              gradientText3="voters"
              gradientText4="companies"
              gradientText5="country"
              gradientText6="society"
              withAnimation={true}
            />
          </div>
          <div className="flex flex-col lg:mt-10 gap-4 lg:gap-7">
            <div className="flex md:hidden w-full items-center justify-center">
              <Button
                text="Get Started"
                link="https://app.subconscious.ai/"
                data-gtm="get-started-button"
              />
            </div>
            <p className="text-white text-sm lg:text-xl">
              Conduct Causal Market Research faster, with higher quality and
              more ethically than existing methods, with guaranteed human-level
              reliability.
            </p>
          </div>

          <div className="flex mt-8 lg:mt-16 w-[342px] md:w-[580px] lg:w-[820px] items-center justify-evenly ">
            <DynamicInput />
          </div>
        </div>

        {/* OUR PARTNERS SECTION*/}

        {partnersSection}

        <div className="line-breaker md:-mt-4 mb-4"></div>
        {/* AGENTS SECTION*/}

        <div className="flex flex-col">
          <Heading withAnimation={false} whiteText="Subconscious AI Agents." />

          <Heading
            withAnimation={false}
            whiteText="Digital Twin of the Consumer."
          />

          <div className="flex gap-4 md:gap-12 justify-evenly items-center mt-8">
            <div>
              <Image
                className=""
                src="circle1.svg"
                width={40}
                height={40}
                alt="agents"
              />
            </div>
            <div>
              <Image
                className=""
                src="circle2.svg"
                width={60}
                height={60}
                alt="agents"
              />
            </div>
            <div>
              <Image
                className=""
                src="circle3.svg"
                width={90}
                height={90}
                alt="agents"
              />
            </div>
            <div>
              <Image
                className=""
                src="circle4.svg"
                width={160}
                height={160}
                alt="agents"
              />
            </div>
            <div>
              <Image
                className=""
                src="circle5.svg"
                width={90}
                height={90}
                alt="agents"
              />
            </div>
            <div>
              <Image
                className=""
                src="circle2.svg"
                width={60}
                height={60}
                alt="agents"
              />
            </div>
            <div>
              <Image
                className=""
                src="circle1.svg"
                width={40}
                height={406}
                alt="agents"
              />
            </div>
          </div>

          <p className="text-white text-center text-roboto text-sm lg:text-xl mt-8">
            Our causal models get better the more you use them.
            <br />
            <br />
            We have a current population of 127 million synthetic respondents
            generated on data from 800 million human respondents in order to
            improve the realism of these respondents, back-tested against 100
            years of Economics, Psychology and Sociology experiments.
          </p>
        </div>

        <div className="flex flex-col mt-16 gap-12 md:gap-32">
          <div className="w-full flex flex-col md:flex-row gap-12">
            <div className="md:w-1/2 flex flex-col gap-4">
              <p className="text-white font-semibold text-base md:text-[28px]">
                Instant User Journey Simulation
              </p>
              <p className="text-white text-sm md:text-base font-normal self-stretch">
                Simulate the full user journey in seconds — no integration
                required. We analyze your website and your competitors&apos;,
                pinpointing key drivers that transition users from awareness to
                engagement, consideration, and ultimately, purchase.
              </p>
              <p className="text-[#AFAFAF] text-sm md:text-base text-normal">
                REPLACES
              </p>
              <div className="flex max-w-fit justify-evenly pr-16 items-center gap-4">
                <div>
                  <Image
                    className=""
                    src="hotjar.svg"
                    width={200}
                    height={260}
                    loading="lazy"
                    alt="Ask a question graphic"
                  />
                </div>
                <div>
                  <Image
                    className=""
                    src="pendo.svg"
                    width={200}
                    height={260}
                    loading="lazy"
                    alt="Ask a question graphic"
                  />
                </div>
                <div>
                  <Image
                    className=""
                    src="amplitude.svg"
                    width={200}
                    height={260}
                    loading="lazy"
                    alt="Ask a question graphic"
                  />
                </div>
                <div>
                  <Image
                    className=""
                    src="mixpanel.svg"
                    width={200}
                    height={260}
                    loading="lazy"
                    alt="Ask a question graphic"
                  />
                </div>
              </div>
            </div>
            <div className="md:w-1/2">
              <CardWithImage
                imageSrc="instant-user-journey.svg"
                threed={true}
                altText="Product Managers"
                title="The fastest way to get answers"
                content="Conduct causal experiments for any human behavior to predict market impacts and craft messages that resonate, all in minutes."
              />
            </div>
          </div>
          <div className="w-full flex flex-col-reverse md:flex-row gap-12">
            <div className="md:w-1/2">
              <CardWithImage
                imageSrc="human-in-loop.svg"
                threed={true}
                altText="Product Managers"
                title="The fastest way to get answers"
                content="Conduct causal experiments for any human behavior to predict market impacts and craft messages that resonate, all in minutes."
              />
            </div>
            <div className="md:w-1/2 flex flex-col gap-4">
              <p className="text-white text-left font-semibold text-base md:text-[28px]">
                Human in the Loop
              </p>
              <p className="text-white text-left text-sm md:text-base font-normal self-stretch">
                We employ a cyclical approach, alternating between quantitative
                analysis, qualitative insights, and human validation. This is
                central to our RLHF strategy for developing a human-level
                language model. We involve humans strictly for validation,
                optimizing costs, speed, and accuracy.
              </p>
              <p className="text-[#AFAFAF] text-left text-sm md:text-base text-normal">
                REPLACES
              </p>
              <div className="flex max-w-fit justify-evenly pr-16 items-center gap-4">
                <div>
                  <Image
                    className=""
                    src="aws.svg"
                    width={200}
                    height={260}
                    loading="lazy"
                    alt="Ask a question graphic"
                  />
                </div>
                <div>
                  <Image
                    className=""
                    src="quatrics.svg"
                    width={120}
                    height={260}
                    loading="lazy"
                    alt="Ask a question graphic"
                  />
                </div>
                <div>
                  <Image
                    className=""
                    src="survey-monkey.svg"
                    width={200}
                    height={260}
                    loading="lazy"
                    alt="Ask a question graphic"
                  />
                </div>
              </div>
            </div>
          </div>
          <div className="w-full flex flex-col md:flex-row gap-12">
            <div className="md:w-1/2 flex flex-col gap-4">
              <p className="text-white font-semibold text-base md:text-[28px]">
                Data Augmentation
              </p>
              <div className="text-white text-sm flex flex-col gap-2 md:text-base font-normal self-stretch">
                <p>Imbalanced data? Hard to find respondents?</p>

                <p>
                  Bring us your customer data, and we&apos;ll generate synthetic
                  consumers for you, enriching your datasets for deeper, more
                  accurate market research.
                </p>
                <p>
                  Our advanced techniques expand and refine your data, enhancing
                  your insights and decision-making capabilities.
                </p>
              </div>
              <p className="text-[#AFAFAF] text-sm md:text-base text-normal">
                REPLACES
              </p>
              <div className="flex">
                <div className="rounded-[35px] flex gap-2 p-2 px-4 border-[#b8b5ff1c] bg-[#b4b2f71a]">
                  <Image
                    className=""
                    src="star-vect.svg"
                    width={20}
                    height={20}
                    priority
                    alt="Ask a question graphic"
                  />
                  Net new capability
                </div>
              </div>
            </div>
            <div className="md:w-1/2">
              <CardWithImage
                imageSrc="data-aug.svg"
                threed={true}
                altText="Product Managers"
                title="The fastest way to get answers"
                content="Conduct causal experiments for any human behavior to predict market impacts and craft messages that resonate, all in minutes."
              />
            </div>
          </div>
          <div className="w-full flex flex-col-reverse md:flex-row gap-12">
            <div className="md:w-1/2">
              <CardWithImage
                imageSrc="causal-mod.svg"
                threed={true}
                altText="Product Managers"
                title="The fastest way to get answers"
                content="Conduct causal experiments for any human behavior to predict market impacts and craft messages that resonate, all in minutes."
              />
            </div>
            <div className="md:w-1/2 flex flex-col gap-4">
              <p className="text-white text-left font-semibold text-base md:text-[28px]">
                Causal Modeling
              </p>
              <div className="text-white flex flex-col gap-2 text-left text-sm md:text-base font-normal self-stretch">
                <p>
                  Would you like to know the &quot;next best action&quot; to
                  take, but don&apos;t want to wait 6 months to collect data? We
                  have the next best thing.
                </p>
                <p>
                  Subconscious AI simplifies the creation and execution of
                  causal surveys, experiments, synthetic respondent creation,
                  and other market research activities by creating a digital
                  twin of the consumer using large numbers of AI agents and
                  language models.
                </p>
              </div>
              <p className="text-[#AFAFAF] text-left text-sm md:text-base text-normal">
                REPLACES
              </p>
              <div className="flex justify-start items-center gap-4">
                <div className="rounded-[35px] flex gap-2 p-2 px-4 border-[#b8b5ff1c] bg-[#b4b2f71a]">
                  <Image
                    className=""
                    src="star-vect.svg"
                    width={20}
                    height={20}
                    priority
                    alt="Ask a question graphic"
                  />
                  Net new capability
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* WHO IS SUBCONSCIOUS FOR? */}
        <div className="grid grid-cols-1 md:mt-20 gap-4 md:grid-cols-2 lg:grid-cols-3 md:gap-y-8 lg:gap-y-10">
          <UserType
            src="/icons/productmanagers.svg"
            altText="Product"
            title="Product"
            content="Determine value of every feature of any product, to any mindset"
          />
          <UserType
            src="/icons/marketingmanagers.svg"
            altText="Marketing"
            title="Marketing"
            content="Segment any audience by need instead of by demographic, for any medium"
          />
          <UserType
            src="/icons/policymakers.svg"
            altText="Policy"
            title="Policy"
            content="Simulate policy outcomes, for any universe of actionable policies"
          />
          <UserType
            src="/icons/healthcare.svg"
            altText="Healthcare"
            title="Healthcare"
            content="Optimize patient care and hospital efficiency, for any healthcare setting"
          />
          <UserType
            src="/icons/salesmanagers.svg"
            altText="Sales"
            title="Sales"
            content="Understand what features are nice-to-have vs need-to-have, for any purchase"
          />
          <UserType
            src="/icons/contentcreators.svg"
            altText="Content Creation"
            title="Content Creation"
            content="Gauge audience preferences to tailor content for increased engagement"
          />
        </div>

        {/* CARDS WITH IMAGE */}
        <div className="line-breaker  mb-4"></div>

        <div className="cards-with-image-section mt-8 md:mt-0 flex flex-col gap-4">
          <div className="cards-with-image-section-row justify-center items-center flex">
            {isMobile ? (
              <CardWithImage
                imageSrc="/experimentdemo.svg"
                threed={false}
                altText="Product Managers"
                title="One platform. Infinite intelligence."
                content="By designing experiments that simulate ∞ product choices across ∞ different scenarios and ∞ different respondents, you can know the desire of any feature, policy, or preference, for any mind."
              />
            ) : (
              <HeroScrollDemo />
            )}
          </div>
          {/* <MacbookScrollDemo /> */}
        </div>

        {/* VIDEO */}
        {/* <div className="h-full">
          <VideoPlayer {...playerOptions} />
        </div> */}

        <div className="underlying-magic-section py-12 flex flex-col gap-6">
          <Title whiteText="The underlying magic" />
          <div className="grid grid-cols-1 md:mt-20 gap-8 md:grid-cols-2 lg:grid-cols-3 md:gap-y-8 lg:gap-y-10">
            <UserType
              src="/icons/nintynine.svg"
              altText="Product"
              title="Accuracy/Cost Tradeoff"
              width={58}
              magicCard={true}
              content="Cost-efficiency doesn't have to compromise on quality. Embrace strategic spending with us experience 99% accuracy at 10% of the cost."
            />
            <UserType
              src="/icons/bulb.svg"
              altText="Marketing"
              magicCard={true}
              width={22}
              title="The fastest way to get answers"
              content="Conduct causal experiments for any human behavior to predict market impacts and craft messages that resonate, all in minutes."
            />
            <UserType
              src="/icons/brain.svg"
              magicCard={true}
              altText="Policy"
              title="Largest Repository of Turing Experiments"
              content="We’ve transcribed the world’s research into a consciousness assessment for AI."
            />
            <UserType
              src="/icons/bot.svg"
              magicCard={true}
              altText="Healthcare"
              title="Causal AI"
              content="Our Causal Graph infers cause-and-effect relationships, allowing for predictions and interventions. "
            />
            <UserType
              src="/icons/ai.svg"
              magicCard={true}
              altText="Sales"
              title="Generative AI"
              content="Simulate any respondent. LLMs design experiments with more divergent thought, faster, and at big-data scale VS human experimental designers."
            />
            <UserType
              src="/icons/society.svg"
              magicCard={true}
              altText="Content Creation"
              title="Digital Twin of Society"
              content="We’ve purchased and aggregated traits and characteristics of many global citizens, enabling reliable simulation of most behaviors. For more specific use cases, bring your own data."
            />
          </div>
        </div>
      </div>

      <div className="line-breaker my-4"></div>

      {/* LOGO LIST */}
      <div className="py-12">
        <LogoList />
      </div>

      <div className="line-breaker my-4 "></div>

      <div className="z-10 mx-6 py-6 mt-20 lg:h-auto lg:max-w-6xl lg:py-12 lg:mx-auto flex flex-col gap-20">
        {/* SCHEDULE A DEMO */}
        <div className="flex flex-col gap-10" id="scheduleDemo">
          <Heading whiteText="Schedule a demo" gradientText="" />
          <Schedule />
        </div>

        {/* RESEARCH WITH SUBCONSCIOUS AI */}
        {/* <div className="flex flex-col gap-10 items-center">
          <Heading whiteText="Research with Subconscious AI" />
          <div className="stripe-subscription-section w-full">
            <SubscriptionCard />
          </div>
        </div> */}

        <div className="line-breaker my-4 "></div>

        {/* WHAT WILL YOU CREATE? */}
        <div className="flex flex-col gap-10 items-center">
          {/* BRAIN */}
          <div className="flex justify-center w-full h-min-fit h-52">
            <Brain />
          </div>
          <div className=" text-3xl lg:text-section-title text-center">
            <Heading whiteText="Built for the future." />

            <Heading whiteText="Available today." />
          </div>
        </div>
      </div>
    </div>
  );
}
