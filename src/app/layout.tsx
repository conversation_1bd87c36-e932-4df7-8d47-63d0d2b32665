import React from "react";
import Header from "@/app/_components/_layout/Header";
import "../styles/globals.css";
import type { Metadata } from "next";
import Footer from "./_components/_layout/Footer";
import { GoogleTagManager } from "@next/third-parties/google";
import { GoogleAnalytics } from "@next/third-parties/google";
import { SpeedInsights } from "@vercel/speed-insights/next";
import dynamic from "next/dynamic";

// Lazy load analytics to prevent render blocking
const Analytics = dynamic(() => import("./api/analytics"), {
  ssr: false,
  loading: () => null,
});

export const metadata: Metadata = {
  title: "subconscious.ai - AI-Powered Causal Market Research",
  description:
    "Conduct Causal Market Research faster, with higher quality and more ethically than existing methods, with guaranteed human-level reliability",
  keywords:
    "AI, market research, causal analysis, subconscious AI, behavioral insights",
  authors: [{ name: "Subconscious AI" }],
  creator: "Subconscious AI",
  publisher: "Subconscious AI",
  robots: "index, follow",
  openGraph: {
    title: "subconscious.ai - AI-Powered Causal Market Research",
    description:
      "Conduct Causal Market Research faster, with higher quality and more ethically than existing methods",
    url: "https://subconscious.ai",
    siteName: "Subconscious AI",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "subconscious.ai - AI-Powered Causal Market Research",
    description:
      "Conduct Causal Market Research faster, with higher quality and more ethically than existing methods",
    creator: "@subconscious_ai",
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <head>
        {/* Preconnect to external domains */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link
          rel="preconnect"
          href="https://fonts.gstatic.com"
          crossOrigin="anonymous"
        />
        <link rel="preconnect" href="https://www.googletagmanager.com" />
        <link rel="preconnect" href="https://www.google-analytics.com" />

        {/* DNS prefetch for performance */}
        <link rel="dns-prefetch" href="//fonts.googleapis.com" />
        <link rel="dns-prefetch" href="//www.googletagmanager.com" />

        {/* Preload critical resources */}
        <link
          rel="preload"
          href="/home-bg-enw.svg"
          as="image"
          type="image/svg+xml"
        />
      </head>
      <GoogleTagManager gtmId="GTM-WXSLXHDB" />
      <body className="bg-background">
        <main className="flex min-h-screen flex-col bg-cover bg-landing-page-bg">
          <Header />
          {children}
          <Footer />
        </main>

        {/* Load analytics and monitoring scripts after main content */}
        <SpeedInsights />
        <Analytics />
        <GoogleAnalytics gaId="G-52WK8DDZLF" />
      </body>
    </html>
  );
}
