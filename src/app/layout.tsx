import React from "react";
import Header from "@/app/_components/_layout/Header";
import "../styles/globals.css";
import type { Metadata } from "next";
import Footer from "./_components/_layout/Footer";
import Analytics from "./api/analytics";
import { GoogleTagManager } from "@next/third-parties/google";
import { GoogleAnalytics } from "@next/third-parties/google";
import { SpeedInsights } from "@vercel/speed-insights/next";

export const metadata: Metadata = {
  title: "subconscious.ai",
  description:
    "Conduct Causal Market Research faster, with higher quality and more ethically than existing methods, with guaranteed human-level reliability",
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <GoogleTagManager gtmId="GTM-WXSLXHDB" />
      <body>
        <main className="flex min-h-screen flex-col bg-background bg-cover bg-landing-page-bg">
          <Header />
          {children}
          <SpeedInsights />
          <Footer />
          <Analytics />
        </main>
      </body>
      <GoogleAnalytics gaId="G-52WK8DDZLF" />
    </html>
  );
}
