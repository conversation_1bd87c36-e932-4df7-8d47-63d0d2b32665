"use client";

import React from "react";
import { PlaceholdersAndVanishInput } from "./ui/placeholders-and-vanish-input";

function DynamicInput() {
  const placeholders = [
    "How can I design a maximally valuable Electric Car?",
    "What factors influence attitudes towards teleworking colleagues?",
    "What immigrant traits influence support for admission into the USA?",
    "Why do enterprises value Business Intelligence or Consulting?",
    "Which Climate Change message maximizes urgency to take action?",
  ];
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    console.log(e.target.value);
  };
  const onSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
  };
  return (
    <div className="flex w-full">
      <div className="w-full flex flex-col justify-center items-center px-4">
        <PlaceholdersAndVanishInput
          placeholders={placeholders}
          onChange={handleChange}
          onSubmit={onSubmit}
        />
      </div>
    </div>
  );
}

export default DynamicInput;
