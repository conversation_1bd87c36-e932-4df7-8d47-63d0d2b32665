"use client";
import React, { useRef, useCallback, useMemo } from "react";
import { useScroll, useTransform, motion, MotionValue } from "framer-motion";

export const ContainerScroll = ({
  titleComponent,
  children,
}: {
  titleComponent: string | React.ReactNode;
  children: React.ReactNode;
}) => {
  const containerRef = useRef<any>(null);
  const { scrollYProgress } = useScroll({
    target: containerRef,
  });
  const [isMobile, setIsMobile] = React.useState(false);

  React.useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };
    checkMobile();

    // Throttle resize events for better performance
    let timeoutId: NodeJS.Timeout;
    const throttledResize = () => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(checkMobile, 100);
    };

    window.addEventListener("resize", throttledResize, { passive: true });
    return () => {
      window.removeEventListener("resize", throttledResize);
      clearTimeout(timeoutId);
    };
  }, []);

  const scaleDimensions = useCallback(() => {
    return isMobile ? [0.7, 0.9] : [1.05, 1];
  }, [isMobile]);

  // Memoize transforms for better performance
  const transforms = useMemo(
    () => ({
      rotate: useTransform(scrollYProgress, [0, 1], [20, 0]),
      scale: useTransform(scrollYProgress, [0, 1], scaleDimensions()),
      translate: useTransform(scrollYProgress, [0, 1], [0, -100]),
    }),
    [scrollYProgress, scaleDimensions]
  );

  return (
    <div
      className="h-[30rem] md:h-[48rem] lg:h-[57rem] flex items-center justify-center relative p-2 md:px-16"
      ref={containerRef}
    >
      <div
        className="w-full  relative"
        style={{
          perspective: "1000px",
        }}
      >
        <Header
          translate={transforms.translate}
          titleComponent={titleComponent}
        />
        <Card
          rotate={transforms.rotate}
          translate={transforms.translate}
          scale={transforms.scale}
        >
          {children}
        </Card>
      </div>
    </div>
  );
};

export const Header = ({ translate, titleComponent }: any) => {
  return (
    <motion.div
      style={{
        translateY: translate,
      }}
      className="div max-w-5xl mx-auto text-center"
    >
      {titleComponent}
    </motion.div>
  );
};

export const Card = React.memo(
  ({
    rotate,
    scale,
    children,
  }: {
    rotate: MotionValue<number>;
    scale: MotionValue<number>;
    translate: MotionValue<number>;
    children: React.ReactNode;
  }) => {
    // Memoize the box shadow to prevent recalculation
    const boxShadow = useMemo(
      () =>
        "0 0 #0000004d, 0 9px 20px #0000004a, 0 37px 37px #00000042, 0 84px 50px #00000026, 0 149px 60px #0000000a, 0 233px 65px #00000003",
      []
    );

    return (
      <motion.div
        style={{
          rotateX: rotate,
          scale,
          boxShadow,
          willChange: "transform", // Optimize for animations
        }}
        className="-mt-12 mx-auto h-fit w-full border-[1.206px] border-white/[0.1] p-2 md:p-6 bg-[#212631] rounded-[30px] shadow-2xl"
      >
        <div className="h-full w-full overflow-hidden rounded-2xl bg-[#212631] dark:bg-[#212631] md:rounded-2xl md:p-4">
          {children}
        </div>
      </motion.div>
    );
  }
);
