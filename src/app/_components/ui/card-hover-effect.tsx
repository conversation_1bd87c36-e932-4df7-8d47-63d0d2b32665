import { cn } from "@/utils/cn";
import { AnimatePresence, motion } from "framer-motion";
import { useState } from "react";
import CardWithImage from "../CardWithImage";
import MagicCard from "../MagicCard";

export const HoverEffect = ({
  items,
  className,
  magicType = false,
}: {
  items: {
    imageSrc: string;
    title: string;
    content: string;
  }[];
  className?: string;
  magicType?: boolean;
}) => {
  let [hoveredIndex, setHoveredIndex] = useState<number | null>(null);

  return (
    <div
      className={cn(
        "flex w-full flex-col md:flex-row ",
        magicType ? "flex-wrap" : "gap-6",
        className
      )}
    >
      {items.map((item, idx) => (
        <div
          key={item?.title}
          className={`relative group ${
            magicType ? "p-4" : ""
          } w-full flex md:w-1/2`}
          onMouseEnter={() => setHoveredIndex(idx)}
          onMouseLeave={() => setHoveredIndex(null)}
        >
          <AnimatePresence>
            {hoveredIndex === idx && (
              <motion.span
                className="absolute inset-0 h-full w-full bg-neutral-200 dark:bg-slate-800/[0.8] block rounded-3xl"
                layoutId="hoverBackground"
                initial={{ opacity: 0 }}
                animate={{
                  opacity: 0.5,
                  transition: { duration: 0.15 },
                }}
                exit={{
                  opacity: 0,
                  transition: { duration: 0.15, delay: 0.2 },
                }}
              />
            )}
          </AnimatePresence>

          {magicType === true ? (
            <MagicCard
              imageSrc={item.imageSrc} // Pass the image source to CardWithImage
              altText={item.title} // You can use the title as alt text
              title={item.title}
              content={item.content}
            />
          ) : (
            <CardWithImage
              imageSrc={item.imageSrc} // Pass the image source to CardWithImage
              altText={item.title} // You can use the title as alt text
              title={item.title}
              content={item.content}
            />
          )}
        </div>
      ))}
    </div>
  );
};
