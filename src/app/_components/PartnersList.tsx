import Image from "next/image";

export default function PartnersList() {
  const COMPANIES_FIRST_ROW = [
    { name: "gcloud", height: 40, width: 170 },
    { name: "azure", height: 50, width: 170 },
    { name: "un<PERSON><PERSON><PERSON>", height: 50, width: 170 },
    { name: "harvard", height: 30, width: 170 },
    { name: "united", height: 42, width: 170 },
    { name: "wandb", height: 25, width: 170 },
    { name: "nvidia", height: 25, width: 170 },
  ];

  return (
    <div className="w-full mx-0 text-center">
      <div className="flex flex-col items-center justify-center gap-4 px-4 md:px-14 mb-10">
        <div className="flex flex-wrap justify-center gap-4 md:justify-center">
          {COMPANIES_FIRST_ROW.map((c) => {
            return (
              <div
                className="h-auto w-auto flex items-start justify-start"
                key={c.name}
                style={{ minHeight: c.height, minWidth: c.width }}
              >
                <Image
                  src={`/logos/${c.name}.svg`}
                  height={c.height}
                  width={c.width}
                  layout="responsive"
                  priority
                  alt={`${c.name} logo`}
                />
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
}
