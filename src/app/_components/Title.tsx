"use client";

interface TitleProps {
  whiteText: string;
  gradientText?: string;
}

function Title(props: TitleProps) {
  return (
    <div className="flex flex-wrap w-full justify-center items-center font-roboto mb-4 text-xl lg:text-3xl font-semibold text-center">
      <p className="text-white">
        {props.whiteText}
        {props.gradientText != "" && (
          <em className="text-transparent not-italic bg-clip-text bg-soft-gradient pl-2 lg:pl-4">
            {props.gradientText}
          </em>
        )}
      </p>
    </div>
  );
}

export default Title;
