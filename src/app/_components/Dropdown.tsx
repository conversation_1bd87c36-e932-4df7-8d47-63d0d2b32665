import React from "react";
import { ChevronRightIcon } from "@heroicons/react/24/solid";
import Image from "next/image";

function Dropdown() {
  const dropdownData = [
    {
      text: "Product Managers",
      img: "/icons/productmanagers.svg",
      subtitle:
        "determine the value of every feature of any product, to any mindset",
    },
    {
      text: "Sales Managers",
      img: "/icons/salesmanagers.svg",
      subtitle:
        "understand what features are nice-to-have vs need-to-have, for any purchase",
    },
    {
      text: "Healthcare Administrators",
      img: "/icons/healthcare.svg",
      subtitle:
        "optimize patient care and hospital efficiency, for any healthcare setting",
    },
    {
      text: "Policymakers",
      img: "/icons/policymakers.svg",
      subtitle:
        "simulate policy outcomes, for any universe of actionable policies",
    },
    {
      text: "Marketing Managers",
      img: "/icons/marketingmanagers.svg",
      subtitle:
        "segment any audience by need instead of by demographic, for any medium",
    },
    {
      text: "Content Creators",
      img: "/icons/contentcreators.svg",
      subtitle:
        "gauge audience preferences to tailor content for increased engagement",
    },
  ];

  return (
    <div className="font-roboto absolute flex flex-col flex-wrap top-full left-0 mt-6 bg-[#ffffff10] p-4 solutions-modal shadow-lg w-[40vw] rounded-xl py-4 z-60">
      <div className="text-white font-normal text-lg ">ROLES</div>
      <div className="flex dropdown-body gap-4 mt-2">
        <div className="dropdown-body-column flex flex-col gap-2">
          {dropdownData.slice(0, 3).map((item, index) => (
            <div className={`dropdown-holder flex flex-col`} key={index}>
              <div className="dropdown-holder-title flex gap-1 items-center text-lg font-medium text-white ">
                <Image src={item.img} alt="img" width={32} height={32} />
                <div className="dropdown-holder-title-text">{item.text}</div>

                <ChevronRightIcon className="w-5 h-5 ml-1 pt-1" />
              </div>
              <div className="dropdown-holder-subtitle flex items-start justify-start text-base font-normal text[#D2D2D2]">
                {item.subtitle}
              </div>
            </div>
          ))}
        </div>
        <div className="dropdown-body-column flex flex-col gap-2">
          {dropdownData.slice(3).map((item, index) => (
            <div
              className={`dropdown-holder flex flex-col `}
              key={index + 3} // Adjust key offset if needed
            >
              <div className="dropdown-holder-title flex gap-1 items-center text-lg font-medium text-white ">
                <Image src={item.img} alt="img" width={32} height={32} />
                <div className="dropdown-holder-title-text">{item.text}</div>

                <ChevronRightIcon className="w-5 h-5 ml-1 pt-1" />
              </div>
              <div className="dropdown-holder-subtitle flex items-start justify-start text-base font-normal text[#D2D2D2]">
                {item.subtitle}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

export default Dropdown;
