"use client";
import { useState, useEffect } from "react";
import { TypewriterEffectSmooth } from "./ui/typewriter-effect";
import { Typewriter, Cursor } from "react-simple-typewriter";

interface HeadingProps {
  whiteText: string;
  gradientText?: string;
  gradientText2?: string;
  gradientText3?: string;
  gradientText4?: string;
  gradientText5?: string;
  gradientText6?: string;
  withAnimation?: boolean;
}

function Heading(props: HeadingProps) {
  const [showFirstTypewriter, setShowFirstTypewriter] = useState(false);
  const [showSecondTypewriter, setShowSecondTypewriter] = useState(false);
  const [showThirdTypewriter, setShowThirdTypewriter] = useState(false);
  const [showFourthTypewriter, setShowFourthTypewriter] = useState(false);
  const [showFifthTypewriter, setShowFifthTypewriter] = useState(false);
  const [showSixthTypewriter, setShowSixthTypewriter] = useState(false);

  useEffect(() => {
    // Reset state when withAnimation prop changes
    setShowFirstTypewriter(false);
    setShowSecondTypewriter(false);
    setShowThirdTypewriter(false);
    setShowFourthTypewriter(false);
    setShowFifthTypewriter(false);
    setShowSixthTypewriter(false);

    if (props.withAnimation) {
      setShowFirstTypewriter(true);
      const timeout1 = setTimeout(() => {
        setShowFirstTypewriter(false);
        setShowSecondTypewriter(true);
      }, 3000); // Adjust the delay as needed

      const timeout2 = setTimeout(() => {
        setShowSecondTypewriter(false);
        setShowThirdTypewriter(true);
      }, 6000); // Adjust the delay as needed

      const timeout3 = setTimeout(() => {
        setShowThirdTypewriter(false);
        setShowFourthTypewriter(true);
      }, 9000); // Adjust the delay as needed

      const timeout4 = setTimeout(() => {
        setShowFourthTypewriter(false);
        setShowFifthTypewriter(true);
      }, 12000); // Adjust the delay as needed

      const timeout5 = setTimeout(() => {
        setShowFifthTypewriter(false);
        setShowSixthTypewriter(true);
      }, 15000); // Adjust the delay as needed

      return () => {
        clearTimeout(timeout1);
        clearTimeout(timeout2);
        clearTimeout(timeout3);
        clearTimeout(timeout4);
        clearTimeout(timeout5);
      };
    }
  }, [props.withAnimation]);

  return (
    <div className="flex flex-wrap w-full justify-center items-center font-roboto text-3xl lg:text-section-title font-semibold text-center">
      <div className="text-white">
        {props.withAnimation ? (
          <div className="flex flex-col md:flex-row justify-center md:gap-2 items-center">
            <span className="whitespace-nowrap text-[1.35rem] md:text-3xl lg:text-5xl">
              {props.whiteText}
            </span>
            {showFirstTypewriter && (
              <TypewriterEffectSmooth
                words={[
                  {
                    text: props.gradientText || "",
                  },
                ]}
                className="flex items-center pt-2 justify-center"
                cursorClassName="bg-soft-gradient"
              />
            )}
            {showSecondTypewriter && (
              <TypewriterEffectSmooth
                words={[
                  {
                    text: props.gradientText2 || "",
                  },
                ]}
                className="flex items-center pt-2 justify-center"
                cursorClassName="bg-soft-gradient"
              />
            )}
            {showThirdTypewriter && (
              <TypewriterEffectSmooth
                words={[
                  {
                    text: props.gradientText3 || "",
                  },
                ]}
                className="flex items-center pt-2 justify-center"
                cursorClassName="bg-soft-gradient"
              />
            )}
            {showFourthTypewriter && (
              <TypewriterEffectSmooth
                words={[
                  {
                    text: props.gradientText4 || "",
                  },
                ]}
                className="flex items-center pt-2 justify-center"
                cursorClassName="bg-soft-gradient"
              />
            )}
            {showFifthTypewriter && (
              <TypewriterEffectSmooth
                words={[
                  {
                    text: props.gradientText5 || "",
                  },
                ]}
                className="flex items-center pt-2 justify-center"
                cursorClassName="bg-soft-gradient"
              />
            )}
            {showSixthTypewriter && (
              <TypewriterEffectSmooth
                words={[
                  {
                    text: props.gradientText6 || "",
                  },
                ]}
                className="flex items-center pt-2 justify-center"
                cursorClassName="bg-soft-gradient"
              />
            )}
          </div>
        ) : (
          <>
            <span className="text-[1.35rem] md:text-3xl lg:text-5xl">
              {props.whiteText}
            </span>
            {props.gradientText !== undefined && props.gradientText !== "" && (
              <em className="text-transparent not-italic bg-clip-text bg-soft-gradient">
                <Typewriter
                  words={[
                    props.gradientText,
                    props.gradientText2 || "",
                    props.gradientText3 || "",
                    props.gradientText4 || "",
                    props.gradientText5 || "",
                    props.gradientText6 || "",
                  ]}
                  typeSpeed={40}
                  deleteSpeed={20}
                  loop={Infinity}
                />
                <Cursor cursorColor="white" />
              </em>
            )}
          </>
        )}
      </div>
    </div>
  );
}

export default Heading;
