"use client";
import React, { useState } from "react";
import arrowIcon from "../../../public/icons/arrow.svg";
import Image from "next/image";

function Faqs() {
  const faqsList = [
    {
      title: "Is there a free trial available?",
      content:
        "Yes, you can run 2 free experiments without having a paid plan.",
      url: false,
    },
    {
      title: "Can I change my plan later?",
      content:
        "Yes, you can change your plan anytime through your account settings.",
      url: false,
    },
    {
      title: "What is your cancellation policy?",
      content:
        "You can cancel anytime. Your subscription will stay active until the end of the current billing period.",
      url: false,
    },
    {
      title: "Can I get a demo?",
      content: "Yes, we offer live demos. Schedule one through our",
      url: true,
    },
  ];

  function AccordionComp({ title, content, borderBottom, url }) {
    console.log("url", url);
    const [expanded, setExpanded] = useState(false);
    const toggleExpanded = () => setExpanded((current) => !current);

    return (
      <div
        className={`flex-none transition-all ${borderBottom && "border-b border-[#ffffff]"}`}
        onClick={toggleExpanded}
      >
        <div className="text-left items-center h-20 select-none flex justify-between flex-row">
          <h5 className="flex-1 text-sm md:text-xl font-medium cursor-pointer">
            {title}
          </h5>
          <div
            className={`flex-none transition-all duration-75 ${expanded && "rotate-180"}`}
          >
            <Image src={arrowIcon} height={18} width={18} alt="check icon" />
          </div>
        </div>
        <div
          className={`pt-0 overflow-hidden transition-all duration-75 ${expanded ? "max-h-40" : "max-h-0"}`}
        >
          <p className="pb-8 text-left text-xs md:text-lg font-normal">
            {content}&nbsp;
            {url == true && (
              <a
                className="cursor-pointer underline"
                href="https://meetings.hubspot.com/avi-yashchin/subconscious-demo"
                target="_blank"
              >
                Calendar.
              </a>
            )}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full pt-20 pb-10">
      <div className="">
        <h2 className="text-3xl pb-5 text-center">
          Frequently asked questions
        </h2>
        <div className="pb-10 text-center">
          Everything you need to know about the product and billing.
        </div>
      </div>

      <div className="">
        <AccordionComp
          title={faqsList[0].title}
          content={faqsList[0].content}
          borderBottom={true}
          url={faqsList[0].url}
        />
        <AccordionComp
          title={faqsList[1].title}
          content={faqsList[1].content}
          borderBottom={true}
          url={faqsList[1].url}
        />
        <AccordionComp
          title={faqsList[2].title}
          content={faqsList[2].content}
          borderBottom={true}
          url={faqsList[2].url}
        />
        <AccordionComp
          title={faqsList[3].title}
          content={faqsList[3].content}
          borderBottom={false}
          url={faqsList[3].url}
        />
      </div>
    </div>
  );
}

export default Faqs;
