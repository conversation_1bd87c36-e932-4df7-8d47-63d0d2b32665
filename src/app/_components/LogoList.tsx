import Image from "next/image";

export default function LogoList() {
  const COMPANIES_FIRST_ROW = [
    { name: "coca-cola", height: 40, width: 122 },
    { name: "unilever", height: 50, width: 45 },
    { name: "nestle", height: 50, width: 145 },
    { name: "loreal", height: 30, width: 170 },
    { name: "oral-b", height: 42, width: 128 },
    { name: "duracell", height: 25, width: 170 },
    { name: "mastercard", height: 44, width: 55 },
    { name: "att", height: 42, width: 100 },
  ];
  const COMPANIES_SECOND_ROW = [
    { name: "pfizer", height: 50, width: 82 },
    { name: "mcdonalds", height: 42, width: 48 },
    { name: "kodak", height: 45, width: 50 },
    { name: "toys-r-us", height: 45, width: 160 },
    { name: "ramada", height: 32, width: 172 },
    { name: "ford", height: 40, width: 104 },
    { name: "microsoft", height: 38, width: 178 },
    { name: "bayer", height: 38, width: 115 },
  ];

  return (
    <div className="w-full h-auto text-white mx-0 py-3 lg:py-12  text-center">
      <p className="font-roboto text-sm lg:text-body-text font-semibold mb-10">
        Methods trusted and used by hundreds of companies
      </p>
      <div className="flex flex-row gap-2 items-center justify-between px-14 mb-10">
        {COMPANIES_FIRST_ROW.map((c) => {
          return (
            <div className="h-auto w-auto" key={c["name"]}>
              <Image
                src={"/logos/" + c["name"] + ".svg"}
                height={c["height"]}
                width={c["width"]}
                style={{
                  height: "auto",
                  objectFit: "contain",
                  position: "relative",
                }}
                alt={c["name"] + " logo"}
              />
            </div>
          );
        })}
      </div>
      <div className="flex flex-row gap-2 items-center justify-between px-14">
        {COMPANIES_SECOND_ROW.map((c) => {
          return (
            <div className="h-auto w-auto" key={c["name"]}>
              <Image
                src={"/logos/" + c["name"] + ".svg"}
                height={c["height"]}
                width={c["width"]}
                style={{
                  height: "auto",
                  objectFit: "contain",
                  position: "relative",
                }}
                alt={c["name"] + " logo"}
              />
            </div>
          );
        })}
      </div>
    </div>
  );
}
