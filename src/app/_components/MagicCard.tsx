"use client";
import Image from "next/image";
import { CardContainer, CardBody, CardItem } from "./ui/3d-card";

interface MagicCardProps {
  imageSrc: string;
  altText: string;
  title: string;
  content: string;
  threed?: boolean;
}

function MagicCard(props: MagicCardProps) {
  return (
    <div className="flex w-full">
      {props.threed ? (
        <div className="w-full h-full">
          <CardContainer className="inter-var font-roboto">
            <CardBody
              className={`flex flex-col relative group/card dark:hover:shadow-2xl dark:hover:shadow-emerald-500/[0.1] dark:bg-[#212631] dark:border-white/[0.12] w-full h-full p-6 rounded-[20px] border-[1.206px] border-white/[0.1] bg-[#212631] `}
            >
              <CardItem translateZ="100" className="">
                <Image
                  src={props.imageSrc}
                  height={50}
                  width={100}
                  className="w-1/2 rounded-xl group-hover/card:shadow-xl"
                  alt={props.altText}
                  layout="responsive"
                />
              </CardItem>
              <div
                className={`flex flex-col gap-2 text-white items-center font-roboto text-center`}
              >
                <CardItem
                  translateZ="50"
                  className={`text-lg lg:text-[22px] font-semibold text-white dark:text-white `}
                >
                  {props.title}
                </CardItem>
                <CardItem
                  as="p"
                  translateZ="60"
                  className={`text-white text-base dark:text-white `}
                >
                  {props.content}
                </CardItem>
              </div>
            </CardBody>
          </CardContainer>
        </div>
      ) : (
        <div className="flex flex-col gap-2 z-10 rounded-[20px] border-[1.206px] border-[#ffffff12] bg-[#212631] px-2 py-4 md:px-8 md:py-10 items-start justify-start">
          <div className="flex justify-center w-full items-center">
            <div className="flex items-center justify-center width-[50%]">
              <Image
                src={props.imageSrc}
                layout="responsive"
                width={50}
                height={100}
                alt={props.altText}
              />
            </div>
          </div>
          <div className="flex items-center mt-4 justify-center flex-col gap-4 text-white w-full font-roboto text-center">
            <p className="text-base md:text-xl lg:text-2xl font-semibold text-center">
              {props.title}
            </p>
            <p className="text-sm lg:text-base font-normal text-center px-8">
              {props.content}
            </p>
          </div>
        </div>
      )}
    </div>
  );
}

export default MagicCard;
