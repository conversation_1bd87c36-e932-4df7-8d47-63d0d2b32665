"use client";
import Image from "next/image";

interface CardWithIconProps {
  src: string;
  altText: string;
  title: string;
  content: string;
}

function CardWithIcon(props: CardWithIconProps) {
  return (
    <div className="flex flex-col md:flex-row gap-4 rounded-xl bg-white bg-opacity-[0.15] px-6 py-5 items-center">
      <div className="flex justify-items items-center min-h-fit min-w-fit h-12 w-12">
        <Image src={props.src} width={48} height={48} alt={props.altText} />
      </div>
      <div className="flex flex-col gap-0.5 text-white font-roboto text-center md:text-left">
        <p className="text-lg lg:text-[22px] font-semibold">{props.title}</p>
        <p className="text-base lg:text-lg text-[#D2D2D2] font-normal">
          {props.content}
        </p>
      </div>
    </div>
  );
}

export default CardWithIcon;
