// import { useCallback, useEffect, useState } from "react";
// import videojs from "video.js";
// import "videojs-youtube";

// interface VideoPlayerProps {
//   techOrder: string[];
//   sources: {
//     src: string;
//     type: string;
//   }[];
//   autoplay: boolean;
//   controls: boolean;
//   poster: string;
// }

// const VideoPlayer = (props: VideoPlayerProps) => {
//   const [videoElement, setVideoElement] = useState<HTMLVideoElement | null>(
//     null
//   );
//   const onVideo = useCallback((el: HTMLVideoElement) => {
//     setVideoElement(el);
//   }, []);

//   useEffect(() => {
//     if (videoElement == null) {
//       return;
//     }

//     // our video.js player
//     const player = videojs(videoElement, props);

//     return () => {
//       player.dispose();
//     };
//   }, [props, videoElement]);

//   return (
//     <div data-vjs-player>
//       <video ref={onVideo} className="video-js vjs-fluid"></video>
//     </div>
//   );
// };

// export default VideoPlayer;
