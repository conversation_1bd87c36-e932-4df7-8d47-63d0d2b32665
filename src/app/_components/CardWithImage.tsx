"use client";
import Image from "next/image";
import { Card<PERSON>ontainer, CardBody, CardItem } from "./ui/3d-card";

interface CardWithImageProps {
  imageSrc: string;
  altText: string;
  title: string;
  content: string;
  threed?: boolean;
}

function CardWithImage(props: CardWithImageProps) {
  // Determine the alignment based on the length of the content
  const isCenterAligned = props.content.length > 150; // Adjust the threshold as needed
  // const isSecondCard = props.title === "The fastest way to get answers";

  return (
    <div className="flex w-full">
      {props.threed ? (
        <div className="w-full h-full">
          <CardContainer className="inter-var font-roboto">
            <CardBody
              className={`flex flex-col relative group/card dark:hover:shadow-2xl  w-full h-full rounded-[20px] items-${
                isCenterAligned ? "center" : "start"
              } md:text-${isCenterAligned ? "center" : "left"}`}
            >
              <CardItem translateZ="100" className="w-full h-full ">
                <Image
                  src={props.imageSrc}
                  height={100}
                  width={500}
                  className="w-full object-cover rounded-xl group-hover/card:shadow-xl"
                  alt={props.altText}
                  layout="responsive"
                />
              </CardItem>
            </CardBody>
          </CardContainer>
        </div>
      ) : (
        <div className="w-full h-full">
          <CardContainer className="inter-var font-roboto">
            <CardBody
              className={`flex flex-col relative group/card dark:hover:shadow-2xl dark:hover:shadow-emerald-500/[0.1] dark:bg-[#212631] dark:border-white/[0.12] w-full h-full p-6 rounded-[20px] border-[1.206px] border-white/[0.1] bg-[#212631] items-${
                isCenterAligned ? "center" : "start"
              } md:text-${isCenterAligned ? "center" : "left"}`}
            >
              <div
                className={`flex flex-col gap-2 text-white font-roboto text-center md:text-${
                  isCenterAligned ? "center" : "left"
                }`}
              >
                <CardItem
                  translateZ="50"
                  className={`text-lg lg:text-[22px] font-semibold text-white dark:text-white justify-${
                    isCenterAligned ? "center" : "start"
                  } md:text-${isCenterAligned ? "center" : "left"}`}
                >
                  {props.title}
                </CardItem>
                <CardItem
                  as="p"
                  translateZ="60"
                  className={`text-white text-base  mt-2 dark:text-white items-${
                    isCenterAligned ? "center" : "start"
                  } justify-center md:text-${
                    isCenterAligned ? "center" : "left"
                  }`}
                >
                  {props.content}
                </CardItem>
              </div>
              <CardItem translateZ="100" className="w-full h-full mt-4">
                <Image
                  src={props.imageSrc}
                  height={100}
                  width={500}
                  className="w-full object-cover rounded-xl group-hover/card:shadow-xl"
                  alt={props.altText}
                  layout="responsive"
                />
              </CardItem>
            </CardBody>
          </CardContainer>
        </div>
      )}
    </div>
  );
}

export default CardWithImage;
