"use client";
import React, { useState, useEffect } from "react";
import dynamic from "next/dynamic";

// Lazy load heavy components
const CardWithImage = dynamic(() => import("./CardWithImage"), {
  ssr: false,
  loading: () => <div className="h-96 bg-gray-800 animate-pulse rounded-lg" />,
});

const HeroScrollDemo = dynamic(
  () => import("./TabletDemo").then((mod) => mod.HeroScrollDemo),
  { 
    ssr: false,
    loading: () => (
      <div className="h-96 bg-gray-800 animate-pulse rounded-lg flex items-center justify-center">
        <span className="text-white">Loading Interactive Demo...</span>
      </div>
    ),
  }
);

export default function ResponsiveCardSection() {
  const [isMobile, setIsMobile] = useState<boolean>(false);

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    // Initial check
    handleResize();

    // Event listener for window resize
    window.addEventListener("resize", handleResize);

    // Cleanup
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  return (
    <div className="cards-with-image-section mt-8 md:mt-0 flex flex-col gap-4">
      <div className="cards-with-image-section-row justify-center items-center flex">
        {isMobile ? (
          <CardWithImage
            imageSrc="/experimentdemo.svg"
            threed={false}
            altText="Product Managers"
            title="One platform. Infinite intelligence."
            content="By designing experiments that simulate ∞ product choices across ∞ different scenarios and ∞ different respondents, you can know the desire of any feature, policy, or preference, for any mind."
          />
        ) : (
          <HeroScrollDemo />
        )}
      </div>
    </div>
  );
}
