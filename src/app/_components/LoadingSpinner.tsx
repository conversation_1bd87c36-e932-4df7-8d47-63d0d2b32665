import React from "react";

interface LoadingSpinnerProps {
  message?: string;
  className?: string;
}

export default function LoadingSpinner({ 
  message = "Loading...", 
  className = "h-32" 
}: LoadingSpinnerProps) {
  return (
    <div className={`bg-gray-800 animate-pulse rounded-lg flex items-center justify-center ${className}`}>
      <div className="flex flex-col items-center space-y-4">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
        <span className="text-white text-sm">{message}</span>
      </div>
    </div>
  );
}
