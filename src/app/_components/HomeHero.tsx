"use client";
import React, { useState, useEffect } from "react";
import Heading from "./Heading";
import Button from "./Button";

interface HomeHeroProps {
  className?: string;
}

export default function HomeHero({ className = "" }: HomeHeroProps) {
  const [isMobile, setIsMobile] = useState<boolean>(false);

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    // Initial check
    handleResize();

    // Event listener for window resize
    window.addEventListener("resize", handleResize);

    // Cleanup
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  return (
    <div className={`flex flex-col gap-5 lg:gap-2 text-center items-center ${className}`}>
      <div className="flex flex-col w-fit lg:gap-7">
        <Heading
          whiteText="Understand the behavior of your"
          gradientText="users"
          gradientText2="patients"
          gradientText3="voters"
          gradientText4="companies"
          gradientText5="country"
          gradientText6="society"
          withAnimation={true}
        />
      </div>
      <div className="flex flex-col lg:mt-10 gap-4 lg:gap-7">
        <div className="flex md:hidden w-full items-center justify-center">
          <Button
            text="Get Started"
            link="https://app.subconscious.ai/"
            data-gtm="get-started-button"
          />
        </div>
        <p className="text-white text-sm lg:text-xl">
          Conduct Causal Market Research faster, with higher quality and more
          ethically than existing methods, with guaranteed human-level
          reliability
        </p>
        <div className="hidden md:flex w-full items-center justify-center">
          <Button
            text="Get Started"
            link="https://app.subconscious.ai/"
            data-gtm="get-started-button"
          />
        </div>
      </div>
    </div>
  );
}
