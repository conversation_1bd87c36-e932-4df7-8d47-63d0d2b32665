"use client";
import Image from "next/image";

interface UserTypeProps {
  src: string;
  altText: string;
  title: string;
  content: string;
  magicCard?: boolean;
  width?: number;
}

function UserType(props: UserTypeProps) {
  return (
    <div className="flex flex-col gap-2 px-8 items-start justify-start">
      {props.magicCard ? (
        <div className="user-type-row flex flex-col gap-2 items-start justify-start">
          <Image
            src={props.src}
            width={props.width || 28}
            height={24}
            alt={props.altText}
          />
          <p className="text-white font-semibold text-base md:text-[22px]">
            {props.title}
          </p>
          {/* <ChevronRightIcon className="w-5 h-5 pt-1" /> */}
        </div>
      ) : (
        <div className="user-type-row flex gap-2 items-center justify-start">
          <Image src={props.src} width={24} height={24} alt={props.altText} />
          <p className="text-white font-semibold text-base md:text-[22px]">
            {props.title}
          </p>
          {/* <ChevronRightIcon className="w-5 h-5 pt-1" /> */}
        </div>
      )}

      <div className="user-type-row flex justify-start items-start text-left">
        <p className="flex justify-start text left text-sm md:text-base font-normal text-[#D2D2D2]">
          {props.content}
        </p>
      </div>
    </div>
  );
}

export default UserType;
