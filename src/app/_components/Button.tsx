"use client";

import { useRouter } from "next/navigation";

interface ButtonProps {
  text: string;
  link: string;
}

function Button(props: ButtonProps) {
  const router = useRouter();
  return (
    <button
      className="p-1 rounded-full bg-soft-gradient"
      onMouseDown={() => router.push(props.link)}
      aria-label={props.text}
    >
      <span className="block text-white text-roboto px-8 py-3 text-base font-semibold rounded-full bg-[#171C3C] hover:bg-transparent transition duration-300">
        {props.text}
      </span>
    </button>
  );
}

export default Button;
