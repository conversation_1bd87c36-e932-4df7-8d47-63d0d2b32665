import React, { useRef } from "react";
import { Mesh, Vector3 } from "three";
import { <PERSON><PERSON>, use<PERSON>rame, useLoader } from "@react-three/fiber";
import { GLTFLoader } from "three/addons/loaders/GLTFLoader.js";
import { OrbitControls } from "@react-three/drei";

function BrainComponent() {
  const mesh = useRef<Mesh>(null!);
  const gltf = useLoader(GLTFLoader, "/brain-mesh.gltf");

  useFrame(() => {
    mesh.current.rotateOnAxis(new Vector3(0, 1, 0), 0.005);
  });

  return (
    <mesh ref={mesh} scale={0.85}>
      <primitive object={gltf.scene} />
    </mesh>
  );
}

function Brain() {
  return (
    <Canvas>
      <ambientLight intensity={0.5} />
      <spotLight position={[10, 10, 10]} angle={0.15} penumbra={1} />
      <BrainComponent />
      <OrbitControls enableZoom={false} />
    </Canvas>
  );
}

export default Brain;
