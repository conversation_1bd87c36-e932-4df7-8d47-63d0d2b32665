"use client";
import React from "react";
import { ContainerScroll } from "./ui/container-scroll-animation";
import Image from "next/image";

export function HeroScrollDemo() {
  return (
    <div className="flex flex-col overflow-hidden">
      <ContainerScroll
        titleComponent={
          <>
            <h1 className="text-4xl font-semibold text-black dark:text-white">
              One Platform.
              <br />
              <span className="text-4xl md:text-[6rem] font-bold mt-1 leading-none">
                Infinite Intelligence
              </span>
            </h1>
          </>
        }
      >
        <Image
          src={`/experimentdemo.svg`}
          alt="hero"
          height={720}
          layout="responsive"
          width={1400}
          className="mx-auto rounded-2xl object-cover h-full object-left-top"
          draggable={false}
        />
      </ContainerScroll>
    </div>
  );
}
