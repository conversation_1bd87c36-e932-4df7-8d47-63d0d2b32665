"use client";

import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/navigation";

function Footer() {
  const router = useRouter();

  const footerData = [
    {
      title: "Product",
      links: [
        {
          title: "Wiki",
          url: "https://wiki.subconscious.ai/",
        },
        {
          title: "Request a feature",
          url: "https://subconscious.ideas.aha.io/",
        },
        {
          title: "Pricing",
          url: "https://www.subconscious.ai/pricing",
        },
      ],
    },
    {
      title: "Company",
      links: [
        {
          title: "About us",
          url: "/company",
        },
        {
          title: "Careers",
          url: "http://jobs.subconscious.ai/",
        },
      ],
    },
  ];

  const footerDataNew = [
    {
      title: "Resources",
      links: [
        {
          title: "Privacy Policy",
          url: "/policy",
        },
        {
          title: "Terms of Service",
          url: "/terms",
        },
      ],
    },
    {
      title: "Developers",
      links: [
        {
          title: "API",
          url: "https://docs.subconscious.ai/dashboard-api/api-reference/experiments/create-experiment-async",
        },
        {
          title: "Github",
          url: "https://github.com/Subconscious-ai",
        },
      ],
    },
  ];

  return (
    <div className="z-10 mx-6 py-6 w-full max-w-fit lg:max-w-none lg:h-auto lg:py-12 lg:mx-auto flex flex-col gap-5 md:gap-20">
      {/* DISCORD */}
      <div className="flex flex-col gap-5 md:gap-10 items-center text-white">
        <div className="flex justify-between max-w-fit lg:max-w-5xl xl:max-w-7xl items-center w-full bg-white bg-opacity-[0.15] rounded-lg p-3 lg:py-6 lg:px-9 ring-1 ring-inset ring-white/20">
          <div className="flex flex-row gap-2 md:gap-8 align-center items-center">
            <Image
              className="rounded-md bg-white/10 ring-1 ring-inset ring-white/20"
              src={"discord-logo.svg"}
              height={48}
              width={48}
              alt="Discord Logo"
            />
            <div className="flex flex-col gap-0 md:gap-1 font-roboto">
              <p className="font-medium text-sx md:text-[26px] leading-8">
                Join our awesome community
              </p>
              <p className="text-xs md:text-xl font-normal">
                Share results, seek support and stay updated with new releases
              </p>
            </div>
          </div>
          <div>
            <button
              className="bg-white p-2 md:py-3 md:px-4 text-[#030815] rounded-lg text-xs lg:text-lg font-roboto font-medium w-fit"
              onMouseDown={() => router.push("https://discord.gg/3bgj4ZhABz")}
            >
              Join our Discord
            </button>
          </div>
        </div>

        <div className="line-breaker-new "></div>

        <div className="flex flex-col xl:max-w-7xl md:flex-row w-full gap-2 items-start md:justify-between text-xs lg:text-xl font-roboto text-gray">
          <div className="flex flex-col gap-4">
            <Link href="/">
              <Image
                src="logo-with-text.svg"
                width={219}
                height={32}
                layout="responsive"
                alt="subconscious.ai logo"
              />
            </Link>
            <p className="text-[#667085] font-normal text-sm lg:text-base xl:text-lg">
              <EMAIL>
            </p>
            <div className="flex mt-2 gap-4 xl:gap-8">
              <Link
                rel="noopener noreferrer"
                target="_blank"
                href="https://twitter.com/subconscious_ai?s=21&t=s_39OCyUmq22EI9B6hlzZA"
              >
                <Image
                  src="x.svg"
                  width={32}
                  height={32}
                  layout="responsive"
                  alt="x logo"
                />
              </Link>
              <Link
                rel="noopener noreferrer"
                target="_blank"
                href="https://www.linkedin.com/company/subconscious-ai/"
              >
                <Image
                  src="linkedin.svg"
                  width={32}
                  height={32}
                  layout="responsive"
                  alt="x logo"
                />
              </Link>
              <Link
                rel="noopener noreferrer"
                target="_blank"
                href="https://discord.gg/3bgj4ZhABz"
              >
                <Image
                  src="discord.svg"
                  width={32}
                  height={32}
                  layout="responsive"
                  alt="x logo"
                />
              </Link>
              <Link
                rel="noopener noreferrer"
                target="_blank"
                href="https://github.com/Subconscious-ai"
              >
                <Image
                  src="github.svg"
                  width={32}
                  height={32}
                  layout="responsive"
                  alt="x logo"
                />
              </Link>
            </div>
          </div>
          <div className="flex mt-4 md:mt-0 flex-col md:flex-row gap-4 md:gap-6 lg:gap-14 w-full md:w-auto">
            <div className="flex md:gap-6 lg:gap-14">
              {footerData.map((data) => (
                <div
                  key={data.title}
                  className="flex flex-col gap-2 items-start w-full"
                >
                  <p className="text-white text-base lg:text-lg font-semibold">
                    {data.title}
                  </p>
                  {data.links.map((link) => (
                    <Link
                      key={link.title}
                      href={link.url}
                      className="text-[#667085] hover:underline font-normal text-sm lg:text-base"
                    >
                      {link.title}
                    </Link>
                  ))}
                </div>
              ))}
            </div>
            <div className="flex md:gap-6 lg:gap-14">
              {footerDataNew.map((data) => (
                <div
                  key={data.title}
                  className="flex flex-col gap-2 items-start w-full"
                >
                  <p className="text-white text-base lg:text-lg font-semibold">
                    {data.title}
                  </p>
                  {data.links.map((link) => (
                    <Link
                      key={link.title}
                      href={link.url}
                      className="text-[#667085] hover:underline font-normal text-sm lg:text-base"
                    >
                      {link.title}
                    </Link>
                  ))}
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default Footer;
