import { useState } from "react";
import NavLink from "./NavLink";
import { ChevronLeftIcon } from "@heroicons/react/24/solid";
import Image from "next/image";

interface MobileMenuProps {
  open: boolean;
  // eslint-disable-next-line no-unused-vars
  setMenuOpen: (boolean) => void;
}

function MobileMenu({ open, setMenuOpen }: MobileMenuProps) {
  const [showSolutionsContent, setShowSolutionsContent] = useState(false);
  const [activeNavLink, setActiveNavLink] = useState("Home");

  const toggleContent = () => {
    setShowSolutionsContent(!showSolutionsContent);
  };

  const handleNavLinkClick = (text) => {
    setActiveNavLink(text);
    console.log("Active Nav Link: ", activeNavLink);
  };

  const solutionsData = [
    {
      text: "Product Managers",
      img: "/icons/productmanagers.svg",
    },
    {
      text: "Sales Managers",
      img: "/icons/salesmanagers.svg",
    },
    {
      text: "Healthcare Administrators",
      img: "/icons/healthcare.svg",
    },
    {
      text: "Policymakers",
      img: "/icons/policymakers.svg",
    },
    {
      text: "Marketing Managers",
      img: "/icons/marketingmanagers.svg",
    },
    {
      text: "Content Creators",
      img: "/icons/contentcreators.svg",
    },
  ];

  return (
    <div
      className={`z-50 flex flex-col overflow-hidden ${open ? "h-fit" : "h-0"}`}
    >
      {showSolutionsContent ? ( // Show Solutions content
        <div className="flex flex-col items-start justify-center gap-4 px-6">
          <div
            onMouseDown={toggleContent}
            className="mobile-menu-solutions-back-btn items-center text-[#AFAFAF] text-lg font-medium flex gap-2"
          >
            <ChevronLeftIcon className="w-5 h-5 " />
            Back to main menu
          </div>
          <div className="mobile-menu-solutions-options w-full items-center justify-center">
            <div className="mobile-menu-solutions-option-holder flex flex-col gap-4 items-center justify-center">
              {solutionsData.map((item, index) => (
                <div
                  className="mobile-menu-solutions-option flex flex-col gap-2"
                  key={index}
                >
                  <div className="mobile-menu-solutions-option-title flex gap-1 items-center text-lg font-medium text-white ">
                    <Image src={item.img} alt="img" width={32} height={32} />
                    <div className="mobile-menu-solutions-option-title-text">
                      {item.text}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      ) : (
        // Show original content
        <div className="flex flex-col items-center justify-center gap-4">
          <NavLink
            text="Home"
            route="/"
            setMenuOpen={setMenuOpen}
            onClick={() => handleNavLinkClick("Home")}
            activeNavLink={activeNavLink}
          />

          {/* <NavLink
            text="Solutions"
            withSideArrow={true}
            setMenuOpen={setMenuOpen}
            onMouseDown={toggleContent} // Toggle content on NavLink click
          /> */}
          <NavLink
            text="About Us"
            route="/company"
            setMenuOpen={setMenuOpen}
            onClick={() => handleNavLinkClick("About Us")}
            activeNavLink={activeNavLink}
          />
          <NavLink
            text="Pricing"
            route="/pricing"
            setMenuOpen={setMenuOpen}
            onClick={() => handleNavLinkClick("Pricing")}
            activeNavLink={activeNavLink}
          />
          <NavLink
            text="Research"
            route="/research"
            setMenuOpen={setMenuOpen}
            onClick={() => handleNavLinkClick("Research")}
            activeNavLink={activeNavLink}
          />
          {/* <NavLink
            text="Wiki"
            route="https://wiki.subconscious.ai/"
            setMenuOpen={setMenuOpen}
            onMouseDown={() => handleNavLinkClick("Wiki")}
            activeNavLink={activeNavLink}
          /> */}
          <a
            className="flex text-gray text-xl text-roboto h-full justify-center items-center hover:text-white"
            href="https://docs.subconscious.ai/dashboard-api/api-reference/experiments/create-experiment-async"
            target="_blank"
          >
            Docs
          </a>

          {/* <div
            className="flex w-3/4 items-center justify-center"
            onMouseDown={() => setMenuOpen(false)}
          >
            <Button
              text="Get Started"
              link="https://wf371uu1w6g.typeform.com/to/Tob1gFLA"
            />
          </div> */}
        </div>
      )}
    </div>
  );
}

export default MobileMenu;
