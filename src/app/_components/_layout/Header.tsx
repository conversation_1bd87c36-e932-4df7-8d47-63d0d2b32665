"use client";
import { useEffect, useState } from "react";
import Image from "next/image";
import Button from "../Button";
import Link from "next/link";
import { Bars3Icon } from "@heroicons/react/24/outline";
import NavLink from "./NavLink";
import MobileMenu from "./MobileMenu";

function Header() {
  const [menuOpen, setMenuOpen] = useState(false);
  const [isSticky, setIsSticky] = useState(false);
  const [activeNavLink, setActiveNavLink] = useState("Home");

  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 0) {
        setIsSticky(true);
      } else {
        setIsSticky(false);
      }
    };

    window.addEventListener("scroll", handleScroll);

    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, []);

  const handleNavLinkClick = (text) => {
    setActiveNavLink(text);
    console.log("Active Nav Link: ", activeNavLink);
  };

  return (
    <>
      <div
        className={`  ${
          isSticky
            ? "sticky sticky-header top-2 left-0 right-0 z-[100] m-2 rounded-xl bg-[#ffffff12] border border-solid border-white border-opacity-30 backdrop-blur-xl"
            : "transparent-header"
        }`}
      >
        <div
          className={`flex flex-row items-center justify-between mx-6 md:mx-16 my-5 lg:my-5 text-white whitespace-nowrap ${
            isSticky ? "rounded-xl p-2" : ""
          }`}
        >
          <Link href="/">
            <div
              className="md:hidden lg:block"
              onClick={() => handleNavLinkClick("Home")}
            >
              <Image
                src="logo-with-text.svg"
                width={219}
                height={32}
                priority
                alt="subconscious.ai logo"
              />
            </div>
          </Link>
          <div className={`hide-on-mobile  ${isSticky ? "" : ""}`}>
            <div className="flex flex-row h-full md:w-fit md:gap-7 lg:gap-16 justify-center items-center text-gray">
              <NavLink
                text="Home"
                route="/"
                setMenuOpen={setMenuOpen}
                onClick={() => handleNavLinkClick("Home")}
                activeNavLink={activeNavLink}
              />
              <NavLink
                text="About Us"
                route="/company"
                setMenuOpen={setMenuOpen}
                onClick={() => handleNavLinkClick("About Us")}
                activeNavLink={activeNavLink}
              />
              <NavLink
                text="Pricing"
                route="/pricing"
                setMenuOpen={setMenuOpen}
                onClick={() => handleNavLinkClick("Pricing")}
                activeNavLink={activeNavLink}
              />
              <NavLink
                text="Research"
                route="/research"
                setMenuOpen={setMenuOpen}
                onClick={() => handleNavLinkClick("Research")}
                activeNavLink={activeNavLink}
              />
              <a
                className="flex text-gray text-xl text-roboto h-full justify-center items-center hover:text-white"
                href="https://docs.subconscious.ai/dashboard-api/api-reference/experiments/create-experiment-async"
                target="_blank"
              >
                Docs
              </a>
              <Button
                text="Get Started"
                link="https://app.subconscious.ai/"
                data-gtm="get-started-button"
              />
            </div>
          </div>
          <div className={`block md:hidden ${isSticky ? "ml-2" : ""}`}>
            <button
              onMouseDown={() => setMenuOpen(!menuOpen)}
              aria-label="menu icon"
            >
              <Bars3Icon className="fill-white w-8 h-8 md:w-10 md:h-10" />
            </button>
          </div>
        </div>
        <div className="pb-1">
          <MobileMenu open={menuOpen} setMenuOpen={setMenuOpen} />
        </div>
      </div>
    </>
  );
}

export default Header;
