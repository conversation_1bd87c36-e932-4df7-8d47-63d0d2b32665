/* eslint-disable no-unused-vars */
import { useState } from "react";
import { ChevronDownIcon, ChevronRightIcon } from "@heroicons/react/24/solid"; // Assuming you have a down arrow icon available
import Dropdown from "../Dropdown";
import Link from "next/link";

interface NavLinkProps {
  text: string;
  route?: string; // Make route prop optional
  setMenuOpen: (boolean) => void;
  withArrow?: boolean; // Optional prop to indicate whether to display the arrow
  withSideArrow?: boolean;
  onClick?: () => void;
  activeNavLink: string;
}

function NavLink({
  route,
  text,
  setMenuOpen,
  withArrow = false,
  withSideArrow = false,
  onClick,
  activeNavLink,
}: NavLinkProps) {
  const [clicked, setClicked] = useState(false);
  const isActive = text === activeNavLink;

  const handleClick = () => {
    setClicked(!clicked);
    console.log("Clicked");
    if (onClick) {
      console.log("onClick");
      onClick();
    }
    setMenuOpen(false);

    if (onClick) {
      onClick();
    }
  };

  return (
    <div className="relative">
      {route ? ( // Render Link only if route is defined
        <Link
          href={route}
          className={`flex text-gray lg:text-xl text-roboto h-full justify-center items-center hover:text-white ${
            isActive ? "underline underline-offset-8 text-white" : ""
          }`}
          onClick={handleClick}
          prefetch={false}
        >
          <p>{text}</p>
          {withArrow && <ChevronDownIcon className="w-5 h-5 ml-1 pt-1" />}{" "}
          {withSideArrow && <ChevronRightIcon className="w-5 h-5 ml-1 pt-1" />}{" "}
          {/* Display arrow if withArrow prop is true */}
        </Link>
      ) : (
        <div // Render a div if route is not defined
          className={`flex text-gray lg:text-xl text-roboto h-full justify-center items-center hover:text-white ${
            isActive ? "underline underline-offset-8 text-white" : ""
          }`}
          onClick={onClick ? onClick : handleClick}
        >
          <p>{text}</p>
          {withArrow && <ChevronDownIcon className="w-5 h-5 ml-1 pt-1" />}{" "}
          {withSideArrow && <ChevronRightIcon className="w-5 h-5 ml-1 pt-1" />}{" "}
          {/* Display arrow if withArrow prop is true */}
        </div>
      )}
      {withArrow && clicked && <Dropdown />}
    </div>
  );
}

export default NavLink;
