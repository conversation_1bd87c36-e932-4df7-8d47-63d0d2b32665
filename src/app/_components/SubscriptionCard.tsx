"use client";
import Image from "next/image";
import { useRouter } from "next/navigation";
import checkIcon from "../../../public/icons/check.svg";

function SubscriptionCard() {
  const router = useRouter();
  return (
    <div className="w-full">
      <div className="grid lg:grid-cols-3 gap-4 justify-center items-center">
        <div className="rounded-md m-2 min-w-40 h-full">
          <div className="flex flex-col justify-between px-5 py-5 rounded-md w-full border-[#2E3541] bg-[#2E3541] h-full">
            <div className="">
              <div className="flex justify-start pb-4 ">
                <div className="font-semibold text-xl h-10 flex items-center">
                  LAB
                </div>
                <div className=""></div>
              </div>
              <div className="pb-4">
                <div className="font-normal text-4xl">FREE</div>
              </div>
              <div className="font-normal md:text-md pb-4">
                Best for individuals
              </div>
              <div className="pb-4">
                <div className="flex items-center justify-start pb-1">
                  <div className="flex items-center justify-center h-full">
                    <Image
                      src={checkIcon}
                      height={15}
                      width={15}
                      alt="check icon"
                    />
                  </div>
                  <div className="pl-2 text-xs md:text-xl font-normal">
                    2 free experiments
                  </div>
                </div>
                <div className="flex items-center justify-start pb-1">
                  <div className="flex items-center justify-center">
                    <Image
                      src={checkIcon}
                      height={15}
                      width={15}
                      alt="check icon"
                    />
                  </div>
                  <div className="pl-2 text-xs md:text-xl font-normal">
                    All analytics
                  </div>
                </div>
                <div className="flex items-center justify-start pb-1">
                  <div className="flex items-center justify-center">
                    <Image
                      src={checkIcon}
                      height={15}
                      width={15}
                      alt="check icon"
                    />
                  </div>
                  <div className="pl-2 text-xs md:text-xl font-normal">
                    Access to all public experiments
                  </div>
                </div>
              </div>
            </div>
            <div className="flex w-100">
              <button
                className="w-full p-3 rounded-md bg-[#4D515C] font-medium hover:bg-[#ffffff] hover:text-[#171C3C] transition ease-in-out"
                onMouseDown={() => router.push("https://app.subconscious.ai")}
              >
                Get started
              </button>
            </div>
          </div>
        </div>
        <div className="gradientbackground rounded-md m-2 min-w-40 px-0.5 py-0.5 h-full">
          <div className="flex flex-col justify-between px-5 py-5 rounded-md w-full bg-[#2E3541] h-full">
            <div className="">
              <div className="flex justify-between pb-4 items-center">
                <div className="font-semibold h-10 md:text-xl text-lg flex items-center">
                  BUSINESS
                </div>
                <div className="border-2 text-xs border-white rounded-md md:px-3 md:py-2 px-2 py-1">
                  Most popular
                </div>
              </div>
              <div className="flex justify-start pb-4 items-end">
                <div className="font-normal text-4xl">$1,000</div>
                <div className="font-normal text-normal pl-2">per month</div>
              </div>
              <div className="font-normal md:text-md pb-4">
                Best for medium-sized teams
              </div>
              <div className="pb-4">
                <div className="flex items-center justify-start pb-1">
                  <div className="flex items-center justify-center">
                    <Image
                      src={checkIcon}
                      height={15}
                      width={15}
                      alt="check icon"
                    />
                  </div>
                  <div className="pl-2 text-xs md:text-xl font-normal">
                    Unlimited experiments
                  </div>
                </div>
                <div className="flex items-center justify-start pb-1">
                  <div className="flex items-center justify-center">
                    <Image
                      src={checkIcon}
                      height={15}
                      width={15}
                      alt="check icon"
                    />
                  </div>
                  <div className="pl-2 text-xs md:text-xl font-normal">
                    All analytics
                  </div>
                </div>
                <div className="flex items-center justify-start pb-1">
                  <div className="flex items-center justify-center">
                    <Image
                      src={checkIcon}
                      height={15}
                      width={15}
                      alt="check icon"
                    />
                  </div>
                  <div className="pl-2 text-xs md:text-xl font-normal">
                    Access to all public experiments
                  </div>
                </div>
                <div className="flex items-center justify-start pb-1">
                  <div className="flex items-center justify-center">
                    <Image
                      src={checkIcon}
                      height={15}
                      width={15}
                      alt="check icon"
                    />
                  </div>
                  <div className="pl-2 text-xs md:text-xl font-normal">
                    Keep your experiments private
                  </div>
                </div>
                <div className="flex items-center justify-start pb-1">
                  <div className="flex items-center justify-center">
                    <Image
                      src={checkIcon}
                      height={15}
                      width={15}
                      alt="check icon"
                    />
                  </div>
                  <div className="pl-2 text-xs md:text-xl font-normal">
                    Cancel anytime
                  </div>
                </div>
              </div>
            </div>
            <div className="flex w-100">
              <button
                className="w-full p-3 rounded-md text-[#ffffff] font-medium gradientbackground transition ease-in-out hover:bg-[#ffffff]"
                onMouseDown={() =>
                  router.push("https://app.subconscious.ai/settings")
                }
              >
                Get started
              </button>
            </div>
          </div>
        </div>
        <div className="rounded-md m-2 min-w-40 h-full">
          <div className="flex flex-col justify-between px-5 py-5 rounded-md w-full border-[#ffffff12] bg-[#2E3541] h-full">
            <div>
              <div className="flex justify-start pb-4">
                <div className="font-semibold text-xl h-10 flex items-center">
                  ENTERPRISE
                </div>
                <div className=""></div>
              </div>
              <div className="pb-4">
                <div className="font-normal text-4xl">Custom</div>
              </div>
              <div className="font-normal md:text-md pb-4">
                Best for large organizations
              </div>
              <div className="pb-4">
                <div className="flex items-center justify-start pb-1">
                  <div className="flex items-center justify-center">
                    <Image
                      src={checkIcon}
                      height={15}
                      width={15}
                      alt="check icon"
                    />
                  </div>
                  <div className="pl-2 text-xs md:text-xl font-normal">
                    Custom features
                  </div>
                </div>
                <div className="flex items-center justify-start pb-1">
                  <div className="flex items-center justify-center">
                    <Image
                      src={checkIcon}
                      height={15}
                      width={15}
                      alt="check icon"
                    />
                  </div>
                  <div className="pl-2 text-xs md:text-xl font-normal">
                    Bring your own data
                  </div>
                </div>
                <div className="flex items-center justify-start pb-1">
                  <div className="flex items-center justify-center">
                    <Image
                      src={checkIcon}
                      height={15}
                      width={15}
                      alt="check icon"
                    />
                  </div>
                  <div className="pl-2 text-xs md:text-xl font-normal">
                    Unlimited experiments
                  </div>
                </div>
                <div className="flex items-center justify-start pb-1">
                  <div className="flex items-center justify-center">
                    <Image
                      src={checkIcon}
                      height={15}
                      width={15}
                      alt="check icon"
                    />
                  </div>
                  <div className="pl-2 text-xs md:text-xl font-normal">
                    All analytics
                  </div>
                </div>
                <div className="flex items-center justify-start pb-1">
                  <div className="flex items-center justify-center">
                    <Image
                      src={checkIcon}
                      height={15}
                      width={15}
                      alt="check icon"
                    />
                  </div>
                  <div className="pl-2 text-xs md:text-xl font-normal">
                    Access to all public experiments
                  </div>
                </div>
                <div className="flex items-center justify-start pb-1">
                  <div className="flex items-center justify-center">
                    <Image
                      src={checkIcon}
                      height={15}
                      width={15}
                      alt="check icon"
                    />
                  </div>
                  <div className="pl-2 text-xs md:text-xl font-normal">
                    Keep your experiments private
                  </div>
                </div>
                <div className="flex items-center justify-start pb-1">
                  <div className="flex items-center justify-center">
                    <Image
                      src={checkIcon}
                      height={15}
                      width={15}
                      alt="check icon"
                    />
                  </div>
                  <div className="pl-2 text-xs md:text-xl font-normal">
                    Cancel anytime
                  </div>
                </div>
              </div>
            </div>
            <div className="flex w-100">
              <a
                className="w-full p-3 rounded-md bg-[#4D515C] font-medium hover:bg-[#ffffff] hover:text-[#171C3C] transition ease-in-out text-center"
                href="https://meetings.hubspot.com/avi-yashchin/subconscious-demo"
                target="_blank"
              >
                Let&apos;s talk
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default SubscriptionCard;
