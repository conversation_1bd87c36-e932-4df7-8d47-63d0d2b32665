"use client";

interface StepsCardProps {
  number: string;
  title: string;
  content: string;
}

function StepsCard(props: StepsCardProps) {
  return (
    <div className="flex flex-col gap-2 px-8 items-start justify-start w-full">
      <div className="user-type-row flex gap-8 justify-start">
        <p className="text-base md:text-2xl text-[#8E9BE9] font-bold ">
          {props.number}
        </p>
        <p className="text-white font-semibold text-base md:text-2xl text-left">
          {props.title}
        </p>
        {/* <ChevronRightIcon className="w-5 h-5 pt-1" /> */}
      </div>
      <div className="user-type-row flex justify-start items-start text-left">
        <p className="flex justify-start ml-[54px] md:ml-[60px] left text-sm md:text-xl font-normal text-[#D2D2D2]">
          {props.content}
        </p>
      </div>
    </div>
  );
}

export default StepsCard;
