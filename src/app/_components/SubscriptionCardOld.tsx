// "use client";
// import Image from "next/image";
// import { CheckIcon } from "@heroicons/react/24/solid";
// import Link from "next/link";

// function SubscriptionCard() {
//   // Determine the alignment based on the length of the content

//   const includedItems = [
//     { text: "Run unlimited experiments" },
//     { text: "Predict market impacts" },
//     { text: "Tailor unique and effective messaging" },
//     { text: "Measure demand and market share" },
//     { text: "Ongoing customer support" },
//   ];

//   return (
//     <div className="flex flex-col md:flex-row gap-2 rounded-[20px] border-[1.206px] border-[#ffffff12] bg-white bg-opacity-[0.12] w-full items-start justify-start">
//       <div className="flex flex-col px-4 py-8 md:py-16 md:px-10 justify-center w-full md:border-r md:border-[#FFFFFF4D] items-start">
//         <div className="standard-plan-row flex gap-4">
//           <Image
//             src="../../../subconsciousimg.svg"
//             width={60}
//             height={90}
//             alt="logo"
//           />
//           <div className="standard-plan-detail-holder flex flex-col text-white">
//             <p className=" text-sm md:text-lg font-normal">
//               For individuals and companies
//             </p>
//             <p className="text-lg md:text-2xl font-bold">Standard Plan</p>
//           </div>
//         </div>

//         <div className="mt-6 text-sm md:text-lg font-normal">
//           One fixed monthly subscription fee.
//         </div>
//         <div className="text-sm md:text-lg font-normal">Cancel anytime.</div>
//         <div className="flex flex-col items-start justify-center width-[50%] mt-4">
//           <div className="flex items-center">
//             <p className="text-2xl md:text-[54px] font-bold">$1,000</p>
//             <p className="text-sm md:text-xl pt-2 md:pt-4 font-normal">
//               /month
//             </p>
//           </div>
//           <Link
//             href="https://app.subconscious.ai/"
//             className="w-full"
//             data-gtm="get-started-button"
//           >
//             <button className="mt-8 text-sm md:text-lg font-medium get-started-button flex justify-center items-center w-full py-3">
//               Get Started
//             </button>
//           </Link>
//         </div>
//       </div>
//       <div className="flex items-start md:py-16 md:px-10 px-4 py-8  justify-center flex-col text-white w-full  text-center">
//         <div className="font-bold text-base md:text-lg">What’s included</div>
//         {includedItems.map((item, index) => (
//           <div
//             className="flex text-sm md:text-lg font-normal items-start gap-2 mt-4"
//             key={index}
//           >
//             <div>
//               <CheckIcon className="w-5 h-5" />
//             </div>
//             <p className="text-left">{item.text}</p>
//           </div>
//         ))}
//       </div>
//     </div>
//   );
// }

// export default SubscriptionCard;
