export const researchData = [
  {
    text: "Determining the Validity of Large Language Models for Automated Perceptual Analysis",
    href: "https://papers.ssrn.com/sol3/papers.cfm?abstract_id=4241291",
  },
  {
    text: "LLMs with Belief Networks",
    href: "https://arxiv.org/pdf/2406.17232",
  },
  {
    text: "McFadden's Choice Modeling",
    href: "https://eml.berkeley.edu/~mcfadden/nobel/final-nobel.pdf",
  },
  {
    text: "AI-Augmented Surveys: Leveraging Large Language Models and Surveys for Opinion Prediction",
    href: "https://arxiv.org/pdf/2305.09620.pdf",
  },
  {
    text: "More Agents Is All You Need",
    href: "https://arxiv.org/abs/2402.05120",
  },
  {
    text: "Discovery of the Hidden World with Large Language Models",
    href: "https://arxiv.org/pdf/2402.03941.pdf",
  },
  {
    text: "Approaching Human-Level Forecasting with Language Models",
    href: "https://arxiv.org/abs/2402.18563",
  },
  {
    text: "Assessing Causal Reasoning in Language Models",
    href: "https://arxiv.org/pdf/2312.04350.pdf",
  },
  {
    text: "Turning Large Language Models into Cognitive Models",
    href: "https://openreview.net/pdf?id=eiC4BKypf1",
  },
  {
    text: "Using Cognitive Psychology to Understand GPT-3",
    href: "https://openreview.net/pdf?id=eiC4BKypf1",
  },
  {
    text: "Questioning the Survey Responses of Large Language Models",
    href: "https://arxiv.org/html/2306.07951v3",
  },
  {
    text: "Can AI Chatbots Replace Human Subjects in Behavioral Experiments?",
    href: "https://www.science.org/content/article/can-ai-chatbots-replace-human-subjects-behavioral-experiments",
  },
  {
    text: "Out of One, Many: Using Language Models to Simulate Human Samples",
    href: "https://arxiv.org/abs/2209.06899",
  },
  {
    text: "Can AI language models replace human participants?",
    href: "https://www.dropbox.com/s/99wq0yyk6n85bu3/GPT%20Morality.pdf?dl=0",
  },
  {
    text: "Using large language models in psychology",
    href: "https://www.nature.com/articles/s44159-023-00241-5",
  },
  {
    text: "Using GPT for Market Research",
    href: "https://papers.ssrn.com/sol3/papers.cfm?abstract_id=4395751",
  },
  {
    text: "Using Large Language Models to Simulate Multiple Humans and Replicate Human Subject Studies",
    href: "https://arxiv.org/abs/2208.10264",
  },
  {
    text: "Large Language Models as Simulated Economic Agents: What Can We Learn from Homo Silicus?",
    href: "https://arxiv.org/abs/2301.07543",
  },
  {
    text: "Evaluating Large Language Models in Generating Synthetic HCI Research Data: a Case Study",
    href: "https://dl.acm.org/doi/abs/10.1145/3544548.3580688",
  },
  {
    text: "Synthetic data could be better than real data",
    href: "https://www.nature.com/articles/d41586-023-01445-8",
  },
  {
    text: "Large Language Models as Agent Models",
    href: "https://arxiv.org/abs/2212.01681",
  },
  {
    text: "The Challenge of Using LLMs to Simulate Human behavior: A Causal Inference Perspective",
    href: "https://arxiv.org/abs/2312.15524",
  },
  {
    text: "MPI: Evaluating and inducing Personality of Language Models",
    href: "https://arxiv.org/abs/2204.12000",
  },
  {
    text: "Employing large language models in survery",
    href: "https://www.sciencedirect.com/science/article/pii/S2949719123000171",
  },
  {
    text: "AI Personification: Estimating the Personality of Language Models",
    href: "https://arxiv.org/abs/2204.12000",
  },
  {
    text: "Artificial Intelligence from a Psychologist's Point of view",
    href: "https://www.kyb.tuebingen.mpg.de/679134/news_publication_19846421_transferred",
  },
  {
    text: "Language models trained on media diets can predict public opinion",
    href: "https://arxiv.org/pdf/2303.16779.pdf",
  },
  {
    text: "Augmented Language Models: A Survey. Mialon et al.2023.",
    href: "https://arxiv.org/abs/2302.07842",
  },
  {
    text: "Toolformer: Language Models Can Teach Themselves To Use Tools. Shick et al. 2023",
    href: "https://arxiv.org/abs/2302.04761",
  },
  {
    text: "Chain-of-Thought Promting Elicits Reasoning in Large Language Models. Wei et al. 2022",
    href: "https://arxiv.org/abs/2201.11903",
  },
  {
    text: "Synergizing Acting and Reasoning in Language Models. Yao et al. 2022",
    href: "https://arxiv.org/abs/2210.03629",
  },
  {
    text: "Emergent autonomous scientific research capabilities of large language models Boiko et al. 2023",
    href: "https://arxiv.org/pdf/2304.05332.pdf",
  },
  {
    text: "Generative Agents: Interactive Simulacra of Human Behavior. Park et al. 2023",
    href: "https://arxiv.org/pdf/2304.03442.pdf",
  },
  {
    text: "OpenAGI: When LLM Meets Domain Experts. Ge et al. 2023",
    href: "https://arxiv.org/pdf/2304.04370.pdf",
  },
  {
    text: "HuggingGPT: Solving AI Tasks with ChatGPT and its Friends in Hugging Face. Shen et al. 2023",
    href: "https://arxiv.org/pdf/2303.17580.pdf",
  },
  {
    text: "CoMPost: Characterizing and Evaluating Caricature in LLM Simulations",
    href: "https://aclanthology.org/2023.emnlp-main.669/",
  },
  {
    text: "Whose Opinions Do Language Models Reflect? 2023",
    href: "https://arxiv.org/abs/2303.17548",
  },
  {
    text: "Evaluating the Replicability of Social Science Experiments in Nature and Science between 2010 and 2015",
    href: "https://www.nature.com/articles/s41562-018-0399-z",
  },
  {
    text: "DeepMind, Fine-tuning language models to find agreement among humans with diverse preferences",
    href: "https://www.deepmind.com/publications/fine-tuning-language-models-to-find-agreement-among-humans-with-diverse-preferences",
  },
  {
    text: "Jan Leike(OpenAI), A proposal for importing society's values",
    href: "https://aligned.substack.com/p/a-proposal-for-importing-societys-values",
  },
  {
    text: "Microsoft New Future of Work Report 2023",
    href: "https://www.microsoft.com/en-us/research/publication/microsoft-new-future-of-work-report-2023/",
  },
  {
    text: "Relative Value in Large Language Models",
    href: "http://arxiv.org/pdf/2401.14530",
  },
  {
    text: "Incoherent Probability judgments in Large Language Models",
    href: "http://arxiv.org/pdf/2401.16646",
  },
  {
    text: "Framework-base Qualitative Analysis of Free Responses of Large Language Models: Algorithmic Fidelity",
    href: "https://arxiv.org/abs/2309.06364",
  },
  {
    text: "Large language models cannot replace human participants because they cannot portray identity groups",
    href: "https://arxiv.org/pdf/2402.01908",
  },
  {
    text: "Do AIs know what the most important issue is? Using language models to code open-text social survey responses at scale",
    href: "https://journals.sagepub.com/doi/10.1177/20531680241231468",
  },
  {
    text: "Non-compete Agreements in a Rigid Labour Market: The Case of Italy",
    href: "https://docs.iza.org/dp16021.pdf",
  },
  {
    text: "Simulating Human Strategic Behavior: Comparing Single and Multi-agent LLMs",
    href: "https://arxiv.org/pdf/2402.08189",
  },
  {
    text: "Protecting the integrity of survey research",
    href: "https://academic.oup.com/pnasnexus/article/2/3/pgad049/7091615",
  },
  {
    text: "Happy New Year: GPT in 500 lines of SQL",
    href: "https://explainextended.com/2023/12/31/happy-new-year-15/",
  },
  {
    text: "Can You Learn What Helps a Baby Sleep at Night?",
    href: "https://quentinandre.net/post/learning-with-trends/",
  },
  {
    text: "Generative agent-based modeling with actions grounded in physical, social, or digital space using Concordia",
    href: "https://arxiv.org/pdf/2312.03664",
  },
  {
    text: "Wisdom of the Silicon Crowd: LLM Ensemble Prediction Capabilities Rival Human Crowd Accuracy",
    href: "https://arxiv.org/abs/2402.19379",
  },
  {
    text: "Random Silicon Sampling: Simulating Human Sub-Population Opinion Using a Large Language Model Based on Group-Level Demographic Information",
    href: "https://arxiv.org/pdf/2402.18144",
  },
  {
    text: "Automated Social Science: Language Models as Scientist and Subjects",
    href: "https://arxiv.org/abs/2404.11794",
  },
  {
    text: "Using Imperfect Surrogates for Downstream Inference: Design-based Supervised Learning for Social Science Applications of Large Language Models",
    href: "https://naokiegami.com/paper/dsl.pdf",
  },
  {
    text: "Algorithmic Collusion by Large Language Models",
    href: "https://arxiv.org/abs/2404.00806",
  },
  {
    text: "Language Models (Mostly) Know What They Know",
    href: "https://arxiv.org/abs/2207.05221",
  },
  {
    text: "LLM-Blender: Ensembling Large Language Models with Pairwise Ranking and Generative Fusion",
    href: "https://arxiv.org/abs/2306.02561",
  },
  {
    text: "LLM-Ensemble: Optimal Large Language Model Ensemble Method for E-commerce Product Attribute Value Extraction",
    href: "https://arxiv.org/abs/2403.00863",
  },
  {
    text: "Large Language Models as Subpopulation Representative Models: A Review",
    href: "https://arxiv.org/abs/2310.17888",
  },
  {
    text: "Concept Induction: Analyzing Unstructured Text with High-Level Concepts Using LlooM",
    href: "https://hci.stanford.edu/publications/2024/Lam_LLooM_CHI24.pdf",
  },
  {
    text: "Machine Bias. Generative Large Language Models Have a View of Their Own",
    href: "https://osf.io/preprints/socarxiv/r2pnb",
  },
  {
    text: "ChatGPT and Perception Biases in Investments: An Experimental Study",
    href: "https://papers.ssrn.com/sol3/papers.cfm?abstract_id=4787249",
  },
  {
    text: "Let's Think Dot by Dot: Hidden Computation in Transformer Language Models",
    href: "https://arxiv.org/pdf/2404.15758",
  },
  {
    text: "From Words to Numbers: Your Large Language Model Is Secretly A Capable Regressor When Given In-Context Examples",
    href: "https://arxiv.org/abs/2404.07544",
  },
  {
    text: "Language models accurately infer correlations between psychological items and scales from text alone",
    href: "https://osf.io/preprints/psyarxiv/kjuce",
  },
  {
    text: "Assessing Generalization for Subpopulation Representative Modeling via In-Context Learning",
    href: "https://arxiv.org/pdf/2402.07368",
  },
  {
    text: "Measuring Implicit Bias in Explicitly Unbiased Large Language Models",
    href: "https://arxiv.org/pdf/2402.04105",
  },
  {
    text: "Frontiers: Determining the Validity of Large Language Models for Automated Perceptual Analysis",
    href: "https://pubsonline.informs.org/doi/10.1287/mksc.2023.0454",
  },
  {
    text: "CogBench: a large language model walks into a psychology lab",
    href: "https://arxiv.org/abs/2402.18225",
  },
  {
    text: "Measuring What Is Top of Mind",
    href: "https://socialeconomicslab.org/research/measuring-what-is-top-of-mind/",
  },
  {
    text: "In Silico Sociology: Forecasting COVID-19 Polarization with Large Language Models",
    href: "https://t.co/BfkTox1JsH",
  },
  {
    text: "Are Emergent Abilities of Large Language Models a Mirage?",
    href: "https://arxiv.org/abs/2304.15004",
  },
  {
    text: "Algorithmic Collusion by Large Language Models",
    href: "https://arxiv.org/abs/2404.00806",
  },
  {
    text: "In-Context Learning with Long-Context Models: An In-Depth Exploration",
    href: "https://arxiv.org/pdf/2405.00200",
  },
];
