@tailwind base;
@tailwind components;
@tailwind utilities;

@import url("https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,400;0,600;1,400;1,600&display=swap");

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
}

.input-arrow-icon {
  opacity: 1 !important;
}

body {
  color: rgb(var(--foreground-rgb));
  background: linear-gradient(
      to bottom,
      transparent,
      rgb(var(--background-end-rgb))
    )
    rgb(var(--background-start-rgb));
}

.sticky-header {
  transition: top 75ms ease;
}

.transparent-header {
  transition: 0.1ms ease-in;
}

.solutions-modal {
  box-shadow: 0px 1px 16px 0px rgba(0, 0, 0, 0.07);
  backdrop-filter: blur(130px);
}

.line-breaker {
  width: 100%;
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.8) 51.5%,
    rgba(255, 255, 255, 0) 100%
  );
  height: 1px;
}

.line-breaker-new {
  width: 100%;
  background: rgba(255, 255, 255, 0.08);
  height: 1px;
}

.line-breaker-steps {
  opacity: 0.6;
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0.3) -19.96%,
    rgba(255, 255, 255, 0.8) 52.51%,
    rgba(255, 255, 255, 0.4) 120.75%
  );
  height: 1px;
  width: 100%;
}

.border-bottom {
  border-bottom: 1px solid rgba(255, 255, 255, 0.3);
}

.get-started-button {
  border-radius: 78px;
  background: linear-gradient(
    75deg,
    rgba(0, 199, 197, 0.6) 17.79%,
    rgba(82, 119, 247, 0.6) 90.8%,
    rgba(97, 118, 246, 0.6) 113.13%,
    rgba(138, 114, 245, 0.6) 156.38%,
    rgba(187, 109, 243, 0.6) 202.17%
  );
}

.gradientbackground {
  background: linear-gradient(
    75deg,
    rgba(0, 199, 197, 0.8) 17.79%,
    rgba(82, 119, 247, 0.8) 90.8%,
    rgba(97, 118, 246, 0.8) 113.13%,
    rgba(138, 114, 245, 0.8) 156.38%,
    rgba(187, 109, 243, 0.8) 202.17%
  );
}

button.gradientbackground:hover {
  background: #ffffff;
  color: #171c3c;
}

.text-soft-gradient {
  color: transparent;
  background: linear-gradient(
    78deg,
    #7ce2e1 76.39%,
    #7eaffa 82.17%,
    #99a7f9 84.65%,
    #b6a9f0 88.4%,
    #cc92f5 92.36%
  );
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.bg-main {
  --tw-bg-opacity: 1;
  background: rgb(3 8 21 / var(--tw-bg-opacity));
}

.terms-container > div > p {
  font-weight: bold;
}

.agents-bg-gradient {
  border-radius: 896px;
  opacity: 0.4;
  background: radial-gradient(
    50% 50% at 50% 50%,
    #4031a1 0%,
    rgba(51, 39, 124, 0) 100%
  );
}

.policy-table > tbody > tr > td {
  border: 1px solid white;
}

.policy-table > thead > tr > th {
  border: 1px solid white;
}

.hide-on-mobile {
  display: none;
}

@media (min-width: 768px) {
  .hide-on-mobile {
    display: block;
  }
}
