<svg width="454" height="314" viewBox="0 0 454 314" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_1_25797)">
<g filter="url(#filter0_d_1_25797)">
<rect x="6" y="1" width="441" height="313" rx="14.6778" fill="url(#paint0_linear_1_25797)" fill-opacity="0.1" shape-rendering="crispEdges"/>
</g>
<rect x="28.2852" y="38.6797" width="396.286" height="291.731" rx="14.6778" fill="url(#paint1_linear_1_25797)" fill-opacity="0.1"/>
<path d="M126.331 96.2124L117.55 93.6975C113.596 92.5802 109.994 90.4645 107.089 87.5529C104.184 84.6413 102.073 81.0312 100.958 77.0681L98.4486 68.2675C98.3224 67.9595 98.1077 67.6962 97.8319 67.5109C97.556 67.3255 97.2314 67.2266 96.8992 67.2266C96.5671 67.2266 96.2425 67.3255 95.9666 67.5109C95.6907 67.6962 95.476 67.9595 95.3498 68.2675L92.8405 77.0681C91.7257 81.0312 89.6147 84.6413 86.7096 87.5529C83.8045 90.4645 80.2025 92.5802 76.2482 93.6975L67.4672 96.2124C67.13 96.3083 66.8333 96.5119 66.622 96.7921C66.4107 97.0723 66.2964 97.414 66.2964 97.7653C66.2964 98.1165 66.4107 98.4582 66.622 98.7385C66.8333 99.0187 67.13 99.2222 67.4672 99.3181L76.2482 101.833C80.2025 102.95 83.8045 105.066 86.7096 107.978C89.6147 110.889 91.7257 114.499 92.8405 118.463L95.3498 127.263C95.4455 127.601 95.6486 127.898 95.9282 128.11C96.2078 128.322 96.5487 128.437 96.8992 128.437C97.2497 128.437 97.5906 128.322 97.8702 128.11C98.1499 127.898 98.3529 127.601 98.4486 127.263L100.958 118.463C102.073 114.499 104.184 110.889 107.089 107.978C109.994 105.066 113.596 102.95 117.55 101.833L126.331 99.3181C126.668 99.2222 126.965 99.0187 127.176 98.7385C127.388 98.4582 127.502 98.1165 127.502 97.7653C127.502 97.414 127.388 97.0723 127.176 96.7921C126.965 96.5119 126.668 96.3083 126.331 96.2124Z" fill="url(#paint2_linear_1_25797)" fill-opacity="0.3"/>
<ellipse cx="33.7867" cy="20.3637" rx="5.50397" ry="5.50436" fill="#EAECEF" fill-opacity="0.2"/>
<ellipse cx="51.3995" cy="20.3637" rx="5.50397" ry="5.50436" fill="#EAECEF" fill-opacity="0.2"/>
<ellipse cx="69.0108" cy="20.3637" rx="5.50397" ry="5.50436" fill="#EAECEF" fill-opacity="0.2"/>
<ellipse cx="417.596" cy="20.3637" rx="5.50397" ry="5.50436" fill="#EAECEF" fill-opacity="0.2"/>
<rect x="92.4951" y="14.8594" width="302.718" height="11.0087" rx="3.66944" fill="#EAECEF" fill-opacity="0.15"/>
<path d="M377.3 241.76L370.41 239.787C367.307 238.91 364.48 237.25 362.201 234.965C359.921 232.681 358.265 229.848 357.39 226.738L355.421 219.832C355.322 219.591 355.153 219.384 354.937 219.239C354.721 219.093 354.466 219.016 354.205 219.016C353.945 219.016 353.69 219.093 353.473 219.239C353.257 219.384 353.088 219.591 352.989 219.832L351.02 226.738C350.146 229.848 348.489 232.681 346.21 234.965C343.93 237.25 341.104 238.91 338.001 239.787L331.111 241.76C330.846 241.835 330.613 241.995 330.447 242.215C330.282 242.435 330.192 242.703 330.192 242.979C330.192 243.254 330.282 243.522 330.447 243.742C330.613 243.962 330.846 244.122 331.111 244.197L338.001 246.171C341.104 247.047 343.93 248.707 346.21 250.992C348.489 253.277 350.146 256.109 351.02 259.219L352.989 266.125C353.065 266.39 353.224 266.623 353.443 266.79C353.663 266.956 353.93 267.046 354.205 267.046C354.48 267.046 354.748 266.956 354.967 266.79C355.187 266.623 355.346 266.39 355.421 266.125L357.39 259.219C358.265 256.109 359.921 253.277 362.201 250.992C364.48 248.707 367.307 247.047 370.41 246.171L377.3 244.197C377.564 244.122 377.797 243.962 377.963 243.742C378.129 243.522 378.218 243.254 378.218 242.979C378.218 242.703 378.129 242.435 377.963 242.215C377.797 241.995 377.564 241.835 377.3 241.76Z" fill="url(#paint3_linear_1_25797)" fill-opacity="0.3"/>
<ellipse cx="226.429" cy="172.478" rx="79.8944" ry="79.9" fill="url(#paint4_linear_1_25797)" fill-opacity="0.1"/>
<path d="M305.779 172.478C305.779 216.305 270.252 251.834 226.429 251.834C182.605 251.834 147.079 216.305 147.079 172.478C147.079 128.651 182.605 93.1225 226.429 93.1225C270.252 93.1225 305.779 128.651 305.779 172.478Z" stroke="url(#paint5_linear_1_25797)" stroke-opacity="0.2" stroke-width="1.08868"/>
<path d="M240.556 217.553C240.556 218.786 240.066 219.968 239.195 220.84C238.323 221.712 237.14 222.202 235.907 222.202C235.907 224.668 234.928 227.033 233.184 228.777C231.44 230.52 229.075 231.5 226.609 231.5C224.143 231.5 221.778 230.52 220.035 228.777C218.291 227.033 217.311 224.668 217.311 222.202C216.078 222.202 214.896 221.712 214.024 220.84C213.152 219.968 212.662 218.786 212.662 217.553C212.662 216.32 213.152 215.137 214.024 214.266C214.896 213.394 216.078 212.904 217.311 212.904H235.907C237.14 212.904 238.323 213.394 239.195 214.266C240.066 215.137 240.556 216.32 240.556 217.553ZM259.152 171.063C259.154 177.071 257.491 182.961 254.348 188.081C251.204 193.201 246.704 197.35 241.347 200.068C238.185 201.677 236.047 204.731 235.944 208.26L235.907 208.255H217.274C217.172 204.75 215.075 201.686 211.928 200.091C205.6 196.895 200.489 191.72 197.37 185.354C194.252 178.988 193.296 171.778 194.647 164.819C197.028 152.011 207.283 141.644 220.068 139.157C224.8 138.192 229.686 138.291 234.375 139.447C239.063 140.603 243.436 142.787 247.177 145.841C250.918 148.894 253.933 152.742 256.004 157.104C258.075 161.466 259.15 166.234 259.152 171.063ZM237.595 168.938H231.407C231.056 168.938 230.709 168.858 230.392 168.705C230.076 168.552 229.799 168.328 229.582 168.052C229.364 167.776 229.212 167.454 229.137 167.111C229.062 166.768 229.065 166.412 229.148 166.07L231.718 155.433C232.258 153.206 229.952 151.532 228.432 153.048L214.294 167.181C212.881 168.594 213.75 171.272 215.624 171.272H221.514C221.861 171.273 222.203 171.351 222.516 171.501C222.829 171.651 223.104 171.869 223.321 172.139C223.539 172.409 223.693 172.724 223.773 173.062C223.853 173.399 223.856 173.75 223.783 174.089L221.03 186.753C220.538 189.022 222.932 190.617 224.401 189.003L238.999 172.955C240.319 171.5 239.422 168.938 237.595 168.938Z" fill="url(#paint6_linear_1_25797)"/>
</g>
<defs>
<filter id="filter0_d_1_25797" x="-8.67777" y="-13.6778" width="470.356" height="342.356" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="7.33889"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1_25797"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1_25797" result="shape"/>
</filter>
<linearGradient id="paint0_linear_1_25797" x1="214.584" y1="129.435" x2="214.635" y2="314" gradientUnits="userSpaceOnUse">
<stop stop-color="#FAFBFC"/>
<stop offset="1" stop-color="#F9FAFB" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint1_linear_1_25797" x1="226.428" y1="38.6797" x2="225.959" y2="294.04" gradientUnits="userSpaceOnUse">
<stop stop-color="#EAECEF"/>
<stop offset="1" stop-color="#EAECEF" stop-opacity="0"/>
<stop offset="1" stop-color="#EAECEF" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint2_linear_1_25797" x1="96.8992" y1="67.2266" x2="96.8992" y2="128.437" gradientUnits="userSpaceOnUse">
<stop stop-color="#AFAED4"/>
<stop offset="1" stop-color="#D7D6FF" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint3_linear_1_25797" x1="354.205" y1="219.016" x2="354.205" y2="267.046" gradientUnits="userSpaceOnUse">
<stop stop-color="#AFAED4"/>
<stop offset="1" stop-color="#D7D6FF" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint4_linear_1_25797" x1="193.846" y1="92.4846" x2="225.962" y2="263.952" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint5_linear_1_25797" x1="296.182" y1="327.096" x2="160.096" y2="-25.0931" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0.3"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint6_linear_1_25797" x1="226.601" y1="138.5" x2="227" y2="262" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<clipPath id="clip0_1_25797">
<rect width="491" height="314" fill="white" transform="translate(-18)"/>
</clipPath>
</defs>
</svg>
