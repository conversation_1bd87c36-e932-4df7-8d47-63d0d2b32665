<svg className="h-12 w-12" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
<circle cx="24" cy="24" r="24" transform="rotate(-90 24 24)" fill="url(#paint0_linear_1192_961)"/>
<g clip-path="url(#clip0_1192_961)">
<path d="M15.4706 11.2059V33.3824H22.7206C22.9468 33.3824 23.1638 33.4722 23.3237 33.6322C23.4837 33.7921 23.5735 34.0091 23.5735 34.2353C23.5735 34.4615 23.4837 34.6785 23.3237 34.8384C23.1638 34.9984 22.9468 35.0882 22.7206 35.0882H14.6177C14.3914 35.0882 14.1745 34.9984 14.0145 34.8384C13.8546 34.6785 13.7647 34.4615 13.7647 34.2353V10.3529C13.7647 10.1267 13.8546 9.90978 14.0145 9.74982C14.1745 9.58986 14.3914 9.5 14.6177 9.5H33.3824C33.6086 9.5 33.8255 9.58986 33.9855 9.74982C34.1454 9.90978 34.2353 10.1267 34.2353 10.3529V34.2353C34.2353 34.4615 34.1454 34.6785 33.9855 34.8384C33.8255 34.9984 33.6086 35.0882 33.3824 35.0882H32.1029C31.8767 35.0882 31.6598 34.9984 31.4998 34.8384C31.3399 34.6785 31.25 34.4615 31.25 34.2353C31.25 34.0091 31.3399 33.7921 31.4998 33.6322C31.6598 33.4722 31.8767 33.3824 32.1029 33.3824H32.5294V11.2059H15.4706ZM18.0294 15.4706C17.8032 15.4706 17.5863 15.3807 17.4263 15.2208C17.2663 15.0608 17.1765 14.8439 17.1765 14.6176C17.1765 14.3914 17.2663 14.1745 17.4263 14.0145C17.5863 13.8546 17.8032 13.7647 18.0294 13.7647H29.9706C30.1968 13.7647 30.4138 13.8546 30.5737 14.0145C30.7337 14.1745 30.8235 14.3914 30.8235 14.6176C30.8235 14.8439 30.7337 15.0608 30.5737 15.2208C30.4138 15.3807 30.1968 15.4706 29.9706 15.4706H18.0294ZM18.0294 19.7353C17.8032 19.7353 17.5863 19.6454 17.4263 19.4855C17.2663 19.3255 17.1765 19.1086 17.1765 18.8824C17.1765 18.6561 17.2663 18.4392 17.4263 18.2792C17.5863 18.1193 17.8032 18.0294 18.0294 18.0294H29.9706C30.1968 18.0294 30.4138 18.1193 30.5737 18.2792C30.7337 18.4392 30.8235 18.6561 30.8235 18.8824C30.8235 19.1086 30.7337 19.3255 30.5737 19.4855C30.4138 19.6454 30.1968 19.7353 29.9706 19.7353H18.0294ZM18.0294 24C17.8032 24 17.5863 23.9101 17.4263 23.7502C17.2663 23.5902 17.1765 23.3733 17.1765 23.1471C17.1765 22.9208 17.2663 22.7039 17.4263 22.5439C17.5863 22.384 17.8032 22.2941 18.0294 22.2941H29.9706C30.1968 22.2941 30.4138 22.384 30.5737 22.5439C30.7337 22.7039 30.8235 22.9208 30.8235 23.1471C30.8235 23.3733 30.7337 23.5902 30.5737 23.7502C30.4138 23.9101 30.1968 24 29.9706 24H18.0294ZM18.0294 28.2647C17.8032 28.2647 17.5863 28.1748 17.4263 28.0149C17.2663 27.8549 17.1765 27.638 17.1765 27.4118C17.1765 27.1856 17.2663 26.9686 17.4263 26.8086C17.5863 26.6487 17.8032 26.5588 18.0294 26.5588H21.4412C21.6674 26.5588 21.8843 26.6487 22.0443 26.8086C22.2043 26.9686 22.2941 27.1856 22.2941 27.4118C22.2941 27.638 22.2043 27.8549 22.0443 28.0149C21.8843 28.1748 21.6674 28.2647 21.4412 28.2647H18.0294ZM30.3971 32.8096V37.6471C30.3971 38.3102 29.6738 38.7196 29.1053 38.3785L27.4118 37.3622L25.7183 38.3785C25.1498 38.7196 24.4265 38.3102 24.4265 37.6471V32.8096C23.971 32.246 23.6844 31.565 23.5998 30.8454C23.5152 30.1257 23.636 29.3968 23.9483 28.743C24.2605 28.0891 24.7515 27.537 25.3644 27.1505C25.9774 26.764 26.6872 26.5589 27.4118 26.5589C28.1364 26.5589 28.8462 26.764 29.4591 27.1505C30.072 27.537 30.563 28.0891 30.8753 28.743C31.1876 29.3968 31.3084 30.1257 31.2237 30.8454C31.1391 31.565 30.8525 32.246 30.3971 32.8096ZM26.1324 34.0165V36.1403L26.9729 35.6363C27.1055 35.5567 27.2572 35.5147 27.4118 35.5147C27.5664 35.5147 27.718 35.5567 27.8506 35.6363L28.6912 36.1408V34.0169C28.2912 34.1585 27.8604 34.2353 27.4118 34.2353C26.9631 34.2353 26.5324 34.1581 26.1324 34.0165ZM27.4118 32.5294C27.9773 32.5294 28.5197 32.3048 28.9196 31.9049C29.3195 31.505 29.5441 30.9626 29.5441 30.3971C29.5441 29.8315 29.3195 29.2892 28.9196 28.8893C28.5197 28.4894 27.9773 28.2647 27.4118 28.2647C26.8462 28.2647 26.3039 28.4894 25.904 28.8893C25.5041 29.2892 25.2794 29.8315 25.2794 30.3971C25.2794 30.9626 25.5041 31.505 25.904 31.9049C26.3039 32.3048 26.8462 32.5294 27.4118 32.5294Z" fill="white"/>
</g>
<defs>
<linearGradient id="paint0_linear_1192_961" x1="24" y1="0" x2="24" y2="48" gradientUnits="userSpaceOnUse">
<stop stop-color="#9383F1"/>
<stop offset="1" stop-color="#CC97C6"/>
</linearGradient>
<clipPath id="clip0_1192_961">
<rect width="29" height="29" fill="white" transform="translate(9.5 9.5)"/>
</clipPath>
</defs>
</svg>
