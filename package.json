{"name": "subconscious-ai-website", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "eslint --fix 'src/**/*.ts' 'src/**/*.tsx'", "format": "prettier --write 'src/**/*.{js,jsx,ts,tsx,css}'", "dev:with-lint-format": "npm run lint && npm run format && next dev", "prepare": "husky", "analyze": "ANALYZE=true next build"}, "dependencies": {"@eslint/config-array": "^0.18.0", "@eslint/object-schema": "^2.1.4", "@heroicons/react": "^2.0.18", "@next/third-parties": "^14.2.13", "@react-three/drei": "^9.105.4", "@segment/analytics-next": "^1.59.0", "@vercel/speed-insights": "^1.0.12", "clsx": "^2.1.1", "eslint": "^8.56.0", "eslint-config-next": "13.4.19", "framer-motion": "^11.2.4", "js-cookie": "^3.0.5", "next": "^14.2.13", "react": "^18.2.0", "react-dom": "^18.2.0", "react-simple-typewriter": "^5.0.1", "rimraf": "^6.0.1", "tailwind-merge": "^2.3.0", "tailwindcss": "3.3.3", "three": "^0.167.1", "typescript": "^5.2.2"}, "devDependencies": {"@auth0/nextjs-auth0": "^3.1.0", "@next/bundle-analyzer": "^15.3.3", "@react-three/fiber": "^8.14.2", "@types/node": "22.7.9", "@types/react": "^18.2.21", "@types/three": "^0.156.0", "@typescript-eslint/parser": "^7.16.1", "autoprefixer": "^10.4.20", "cross-env": "^7.0.3", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "husky": "^9.0.11", "prettier": "^3.2.5"}}