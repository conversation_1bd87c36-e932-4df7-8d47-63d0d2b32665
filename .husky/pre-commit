#!/bin/sh
. "$(dirname "$0")/_/husky.sh"

echo '🏗️👷 Styling, testing and building your project before committing'

# Check Prettier standards
npm run format ||
(
    echo '🤢🤮🤢🤮 Its FOKING RAW - Your styling needs work. 🤢🤮🤢🤮
            Prettier Check Failed. Run npm run format, add changes and try commit again.';
    false;
)

# Check ESLint Standards
npm run lint ||
(
        echo '😤🏀👋😤 Get that weak thing out of here! 😤🏀👋😤 
                ESLint Check Failed. Make the required changes listed above, add changes and try to commit again.'
        false; 
)


# If everything passes... Now we can commit
echo '🤔🤔🤔🤔... Alright.... Code looks good to me... Trying to test now. 🤔🤔🤔🤔'


# npm run build ||
# (
#     echo '❌👷🔨❌ Better call Bob... Because your build failed ❌👷🔨❌
#             Next build failed: View the errors above to see why. 
#     '
#     false;
# )

# If everything passes... Now we can commit
echo '✅✅✅✅ You win this time... I am committing this now. ✅✅✅✅'