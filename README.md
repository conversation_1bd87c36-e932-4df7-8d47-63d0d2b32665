# Subconscious AI Landing Page

Welcome to the Subconscious AI Landing Page repo! This project aims to develop the primary landing page for our cutting-edge Subconscious AI technology. This README provides setup instructions to get you started on contributing to the project.

## Getting Started

Before you dive into the world of coding with us, there are a few things you need to set up to ensure a smooth experience. This guide assumes you have basic knowledge of Git, Node.js, and npm (Node Package Manager).
\

### Prerequisites

- Git: Make sure you have Git installed on your system. You can download it from git-scm.com.
- Node.js and npm: Our project relies on Node.js and npm. Install them from nodejs.org if you haven't already. This project was built using Node.js version 14.x.x.

### Initial Setup

To set up your environment for the project, follow these steps:

1. #### Clone the Repository

   First, you'll need to clone the project repository to your local machine. To do so, run the following command in your terminal:

   ```bash
   <NAME_EMAIL>:Subconscious-ai/website.git
   ```

2. #### Install Dependencies

   Navigate to the root directory of the project and install the required dependencies:

   ```bash
   cd website
   npm install
   ```

3. #### Start the Development Server

   Once the dependencies are installed, you can start the development server:

   ```bash
   // Normal start
   npm run dev

   // Start with linting and formatting
   npm run dev:with-lint-format
   ```

4. #### Linting and Formatting

   Once the dependencies are installed, you can do the linting and formatting using Eslint and Prettier:

   ```bash
   // Linting
   npm run lint

   // Formatting
   npm run format
   ```

5. #### Accessing the Application

   After starting the development server, the application will be available at http://localhost:3000. Open this URL in your web browser to view the application.

6. #### Making Changes
   Try making a simple text change in the codebase to see if it reflects in the browser. This is a good way to ensure everything is set up correctly.

## Troubleshooting

- If you encounter any issues with npm install, ensure you are using the correct version of Node.js and npm. You might want to consider using nvm (Node Version Manager) to manage your Node.js versions.
- Should you face issues accessing http://localhost:3000, verify that the development server is running and listen for any error messages in the terminal.
