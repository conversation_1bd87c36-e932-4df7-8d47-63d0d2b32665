/* eslint-disable no-unused-vars */
/* eslint-disable prettier/prettier */
import type { Config } from "tailwindcss";
const defaultTheme = require("tailwindcss/defaultTheme");
const colors = require("tailwindcss/colors");
const {
  default: flattenColorPalette,
} = require("tailwindcss/lib/util/flattenColorPalette");

const config: Config = {
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      backgroundImage: {
        "gradient-radial": "radial-gradient(var(--tw-gradient-stops))",
        "gradient-conic":
          "conic-gradient(from 180deg at 50% 50%, var(--tw-gradient-stops))",
        "soft-gradient":
          "linear-gradient(90deg, rgba(124,226,225,1) 0%, rgba(129,175,250,1) 35%, rgba(153,167,249,1) 50%, rgba(182,169,240,1) 75%, rgba(204,146,245,1) 100%)",
        "landing-page-bg": "url('../../public/home-bg-enw.svg')",
        "fade-out-linear":
          "linear-gradient(rgba(255,255,255,0.15) 0%, rgba(255,255,255,0) 100%)",
      },
      colors: {
        background: "#030815",
        gray: "#AFAFAF",
        "light-gray": "D2D2D2",
      },
      fontFamily: {
        roboto: ["Roboto", "sans-serif"],
      },
      fontSize: {
        "section-title": ["56px", { lineHeight: "60px" }],
        "body-text": ["28px", { lineHeight: "40px" }],
        "company-heading": ["40px", { fontWeight: 600 }],
        "company-body": "32px",
        "company-details": "20px",
      },
    },
  },
  plugins: [
    // rest of the code
    addVariablesForColors,
  ],
};

function addVariablesForColors({ addBase, theme }: any) {
  let allColors = flattenColorPalette(theme("colors"));
  let newVars = Object.fromEntries(
    Object.entries(allColors).map(([key, val]) => [`--${key}`, val])
  );

  addBase({
    ":root": newVars,
  });
}
export default config;
